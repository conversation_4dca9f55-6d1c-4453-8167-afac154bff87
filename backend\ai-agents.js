const { nanoid } = require('nanoid');
const fs = require('fs');
const path = require('path');
const {
  getLeadDeveloperCompletion,
  getUserStoryRefinementCompletion,
  getTaskBreakdownCompletion,
  getCodeGenerationCompletion,
  getIntelligentCompletion,
  callOpenRouter
} = require('./ai');
const { AICommentManager } = require('./ai-comments');
const { getDefaultPrompt } = require('./ai-prompts-config');
const db = require('./database');

class AIAgentManager {
  constructor() {
    this.commentManager = new AICommentManager();
    this.agents = {
      productOwner: new ProductOwnerAgent(this.commentManager, this),
      scrumMaster: new ScrumMasterAgent(this.commentManager, this),
      leadDeveloper: new LeadDeveloperAgent(this.commentManager, this),
      developers: []
    };
  }

  /**
   * Load AI prompt from database or use default
   * @param {string} projectId - Project ID
   * @param {string} agentType - Agent type (product_owner, scrum_master, lead_developer)
   * @returns {Promise<string>} Prompt template
   */
  async loadPrompt(projectId, agentType) {
    return new Promise((resolve, reject) => {
      const query = 'SELECT prompt_template FROM ai_prompts WHERE project_id = ? AND agent_type = ?';

      db.get(query, [projectId, agentType], (err, row) => {
        if (err) {
          console.error('Error loading prompt from database:', err);
          // Fall back to default prompt
          const defaultPrompt = getDefaultPrompt(agentType);
          resolve(defaultPrompt ? defaultPrompt.prompt_template : null);
          return;
        }

        if (row) {
          resolve(row.prompt_template);
        } else {
          // No custom prompt found, use default
          const defaultPrompt = getDefaultPrompt(agentType);
          resolve(defaultPrompt ? defaultPrompt.prompt_template : null);
        }
      });
    });
  }

  /**
   * Helper function to get next story number for a project
   */
  async getNextStoryNumber(projectId) {
    return new Promise((resolve, reject) => {
      const query = `SELECT MAX(story_number) as max_number FROM user_stories WHERE project_id = ?`;
      db.get(query, [projectId], (err, row) => {
        if (err) {
          reject(err);
        } else {
          const nextNumber = (row && row.max_number) ? row.max_number + 1 : 1;
          resolve(nextNumber);
        }
      });
    });
  }

  /**
   * Phase 1: Refine story and create tasks (can be done without sprint assignment)
   */
  async refineAndPlanStory(projectId, userStory) {
    console.log(`📋 AI Agents refining and planning user story: ${userStory.title}`);

    try {
      // 0. Log start of refinement
      await this.commentManager.addComment(
        projectId,
        'user_story',
        userStory.id,
        'AIAgentManager',
        `Starting story refinement and task planning: ${userStory.title}`,
        'progress',
        { story_points: userStory.story_points, priority: userStory.priority }
      );

      // 1. Product Owner refines the story (reads comments first)
      const refinedStory = await this.agents.productOwner.refineStory(userStory, projectId);

      // 2. Scrum Master breaks down into tasks (reads comments first)
      const tasks = await this.agents.scrumMaster.breakdownStory(refinedStory, projectId);

      // 3. Update status to 'ready' after tasks are created
      await this.updateStoryStatus(userStory.id, projectId, 'ready');

      // 4. Log completion of planning phase
      await this.commentManager.addComment(
        projectId,
        'user_story',
        userStory.id,
        'AIAgentManager',
        `Completed story refinement and planning. Generated ${tasks.length} tasks. Status: ready for implementation when assigned to sprint.`,
        'progress',
        { tasks_created: tasks.length, phase: 'planning_complete' }
      );

      return {
        refinedStory,
        tasks,
        phase: 'planning_complete'
      };

    } catch (error) {
      console.error('Error in story refinement and planning:', error);

      await this.commentManager.addComment(
        projectId,
        'user_story',
        userStory.id,
        'AIAgentManager',
        `Error during story refinement: ${error.message}`,
        'blocker',
        { error_phase: 'planning' }
      );

      throw error;
    }
  }



  /**
   * Generate user stories for a new project based on name and description
   */
  async generateUserStories(projectId, projectName, projectDescription, techStack) {
    console.log(`🎯 Generating user stories for project: ${projectName}`);

    try {
      // Get the user story generation prompt
      const promptTemplate = await this.loadPrompt(projectId, 'user_story_generator');

      if (!promptTemplate) {
        throw new Error('User story generation prompt template not found');
      }

      // Replace template variables with actual values
      const prompt = promptTemplate
        .replace(/\$\{projectName\}/g, projectName)
        .replace(/\$\{projectDescription\}/g, projectDescription || 'No description provided')
        .replace(/\$\{techStack\}/g, techStack);

      // Use intelligent completion for user story generation
      const context = {
        projectId,
        projectName,
        projectDescription,
        techStack,
        taskType: 'user_story_generation'
      };

      const result = await getIntelligentCompletion(prompt, 'user_story_generation', context);

      // Parse the AI response (handle markdown code blocks)
      let generatedData;
      try {
        let content = result.content.trim();

        // Remove markdown code blocks if present
        if (content.startsWith('```json')) {
          content = content.replace(/^```json\s*/, '').replace(/\s*```$/, '');
        } else if (content.startsWith('```')) {
          content = content.replace(/^```\s*/, '').replace(/\s*```$/, '');
        }

        generatedData = JSON.parse(content);
      } catch (parseError) {
        console.error('Failed to parse AI response:', parseError);
        console.error('Raw AI response:', result.content);
        throw new Error('AI response was not valid JSON');
      }

      if (!generatedData.stories || !Array.isArray(generatedData.stories)) {
        throw new Error('AI response did not contain valid stories array');
      }

      // Create user stories in the database
      const createdStories = [];
      for (const storyData of generatedData.stories) {
        // Get the next story number for this project
        const storyNumber = await this.getNextStoryNumber(projectId);

        const values = [
          projectId,
          storyNumber,
          storyData.title,
          storyData.description,
          storyData.acceptance_criteria,
          storyData.priority || 'medium',
          storyData.story_points || 3,
          storyData.complexity_score || 3,
          storyData.epic || 'Core Features',
          storyData.user_persona || 'user',
          storyData.business_value || '',
          'backlog',
          'not_started'
        ];

        const query = `INSERT INTO user_stories (
          project_id, story_number, title, description, acceptance_criteria, priority,
          story_points, complexity_score, epic, user_persona, business_value, status,
          implementation_status, last_modified_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)`;

        await new Promise((resolve, reject) => {
          db.run(query, values, function(err) {
            if (err) {
              reject(err);
            } else {
              resolve();
            }
          });
        });

        createdStories.push({
          id: this.lastID, // Use the auto-generated ID
          story_number: storyNumber,
          ...storyData,
          project_id: projectId,
          status: 'backlog'
        });
      }

      // Add a comment about the generation
      await this.commentManager.addComment(
        projectId,
        'project',
        projectId,
        'AIAgentManager',
        `Generated ${createdStories.length} user stories automatically based on project description. Stories are ready for review and prioritization.`,
        'progress',
        {
          stories_generated: createdStories.length,
          generation_method: 'automatic',
          ai_model: result.metadata?.selectedModel || 'unknown'
        }
      );

      console.log(`✅ Generated ${createdStories.length} user stories for project: ${projectName}`);
      return createdStories;

    } catch (error) {
      console.error('Error generating user stories:', error);

      // Add error comment
      await this.commentManager.addComment(
        projectId,
        'project',
        projectId,
        'AIAgentManager',
        `Failed to generate user stories: ${error.message}`,
        'blocker',
        { error_phase: 'story_generation' }
      );

      throw error;
    }
  }

  /**
   * Phase 2: Implement the story (requires sprint assignment)
   */
  async processUserStory(projectId, userStory) {
    console.log(`🤖 AI Agents implementing user story: ${userStory.title}`);

    try {
      // Check if story has been refined and planned
      if (userStory.status !== 'ready') {
        console.log(`⚠️ Story ${userStory.title} is not ready for implementation (status: ${userStory.status})`);
        throw new Error('Story must be refined and planned before implementation');
      }

      // 0. Log start of implementation
      await this.commentManager.addComment(
        projectId,
        'user_story',
        userStory.id,
        'AIAgentManager',
        `Starting implementation of user story: ${userStory.title}`,
        'progress',
        { story_points: userStory.story_points, priority: userStory.priority, phase: 'implementation' }
      );

      // Get existing tasks (should already exist from planning phase)
      const existingTasks = await this.getStoryTasks(userStory.id);

      // 1. Lead Developer assigns and reviews tasks
      const assignedTasks = await this.agents.leadDeveloper.assignTasks(existingTasks, projectId, userStory.id);

      // 2. Update status to 'in_progress' when implementation begins
      await this.updateStoryStatus(userStory.id, projectId, 'in_progress');

      // 3. Generate initial code structure
      const codeStructure = await this.agents.leadDeveloper.generateCodeStructure(userStory, projectId);

      // 4. Create files in project workspace
      await this.createProjectFiles(projectId, codeStructure);

      // 5. Mark story as implemented (this will set status to 'testing')
      await this.markStoryImplemented(userStory.id, projectId);

      // 6. Generate progress report
      const report = await this.agents.scrumMaster.generateProgressReport(projectId, userStory, assignedTasks);
      await this.saveReport(projectId, report);

      // 7. Log completion
      await this.commentManager.addComment(
        projectId,
        'user_story',
        userStory.id,
        'AIAgentManager',
        `Completed implementation. Generated ${codeStructure.files?.length || 0} files. Status: testing`,
        'progress',
        {
          files_generated: codeStructure.files?.length || 0,
          tasks_assigned: assignedTasks.length,
          completion_status: 'success',
          current_status: 'testing',
          phase: 'implementation_complete'
        }
      );

      return {
        userStory,
        tasks: assignedTasks,
        codeStructure,
        report,
        phase: 'implementation_complete'
      };
    } catch (error) {
      console.error('Error processing user story:', error);

      // Log error as comment
      await this.commentManager.addComment(
        projectId,
        'user_story',
        userStory.id,
        'AIAgentManager',
        `ERROR during processing: ${error.message}`,
        'blocker',
        { error_type: error.name, stack_trace: error.stack },
        { priority: 'critical' }
      );

      throw error;
    }
  }

  async processSprintStories(projectId, sprintId) {
    console.log(`🚀 AI Agents starting sprint processing for sprint: ${sprintId}`);

    try {
      // Get all stories assigned to this sprint
      const stories = await this.getSprintStories(projectId, sprintId);
      const results = [];

      for (const story of stories) {
        if (story.implementation_status === 'not_started' || story.needs_reimplementation) {
          console.log(`📝 Processing story: ${story.title}`);
          const result = await this.processUserStory(projectId, story);
          results.push(result);
        }
      }

      // Generate sprint summary report
      const sprintReport = await this.agents.scrumMaster.generateSprintReport(projectId, sprintId, results);
      await this.saveReport(projectId, sprintReport, `sprint-${sprintId}`);

      return {
        processedStories: results.length,
        totalStories: stories.length,
        sprintReport
      };
    } catch (error) {
      console.error('Error processing sprint stories:', error);
      throw error;
    }
  }

  async createProjectFiles(projectId, codeStructure) {
    const workspacePath = path.join(__dirname, '..', 'workspaces', projectId, 'src');
    
    for (const file of codeStructure.files) {
      const filePath = path.join(workspacePath, file.path);
      const fileDir = path.dirname(filePath);
      
      // Create directory if it doesn't exist
      fs.mkdirSync(fileDir, { recursive: true });
      
      // Write file content
      fs.writeFileSync(filePath, file.content);
      console.log(`📁 Created file: ${file.path}`);
    }
  }

  async saveReport(projectId, report, prefix = 'report') {
    const reportsPath = path.join(__dirname, '..', 'workspaces', projectId, 'reports');
    const reportFile = path.join(reportsPath, `${prefix}-${Date.now()}.md`);

    fs.writeFileSync(reportFile, report);
    console.log(`📊 Generated report: ${reportFile}`);
  }

  async markStoryImplemented(storyId, projectId) {
    const db = require('./database');

    return new Promise((resolve, reject) => {
      const query = `UPDATE user_stories SET
        implementation_status = 'implemented',
        status = 'testing',
        implemented_in_version = ?,
        needs_reimplementation = FALSE,
        updated_at = CURRENT_TIMESTAMP
        WHERE id = ? AND project_id = ?`;

      const version = `v${Date.now()}`; // Simple version numbering

      db.run(query, [version, storyId, projectId], function(err) {
        if (err) {
          reject(err);
        } else {
          console.log(`✅ Story ${storyId} marked as implemented in ${version} with status 'testing'`);
          resolve(version);
        }
      });
    });
  }

  /**
   * Update the status of a user story
   */
  async updateStoryStatus(storyId, projectId, status) {
    const db = require('./database');

    return new Promise((resolve, reject) => {
      const query = `UPDATE user_stories SET
        status = ?,
        updated_at = CURRENT_TIMESTAMP
        WHERE id = ? AND project_id = ?`;

      db.run(query, [status, storyId, projectId], function(err) {
        if (err) {
          reject(err);
        } else {
          console.log(`✅ Story ${storyId} status updated to '${status}'`);
          resolve(this.changes);
        }
      });
    });
  }

  async getSprintStories(projectId, sprintId) {
    const db = require('./database');

    return new Promise((resolve, reject) => {
      const query = `SELECT * FROM user_stories WHERE project_id = ? AND sprint_id = ? ORDER BY priority DESC`;

      db.all(query, [projectId, sprintId], (err, rows) => {
        if (err) {
          reject(err);
        } else {
          resolve(rows);
        }
      });
    });
  }

  async getStoryTasks(storyId) {
    const db = require('./database');

    return new Promise((resolve, reject) => {
      const query = `SELECT * FROM tasks WHERE story_id = ? ORDER BY created_at ASC`;

      db.all(query, [storyId], (err, rows) => {
        if (err) {
          reject(err);
        } else {
          resolve(rows);
        }
      });
    });
  }
}

class ProductOwnerAgent {
  constructor(commentManager, manager) {
    this.commentManager = commentManager;
    this.manager = manager;
    this.agentName = 'ProductOwnerAgent';
  }

  async refineStory(userStory, projectId) {
    try {
      // Read existing comments first
      const commentsSummary = await this.commentManager.getCommentsSummary(
        projectId,
        'user_story',
        userStory.id
      );

      // Check for blockers
      const blockers = await this.commentManager.getBlockers(
        projectId,
        'user_story',
        userStory.id
      );

      // Log start of refinement
      await this.commentManager.addComment(
        projectId,
        'user_story',
        userStory.id,
        this.agentName,
        'Starting story refinement process. Analyzing requirements and previous feedback.',
        'progress'
      );

      if (blockers.length > 0) {
        const blockerText = blockers.map(b => `- ${b.comment_text}`).join('\n');
        await this.commentManager.addComment(
          projectId,
          'user_story',
          userStory.id,
          this.agentName,
          `Found ${blockers.length} active blockers that need attention:\n${blockerText}`,
          'blocker',
          { blocker_count: blockers.length },
          { priority: 'high' }
        );
      }

      // Load configurable prompt template
      const promptTemplate = await this.manager.loadPrompt(projectId, 'product_owner');

      if (!promptTemplate) {
        throw new Error('No prompt template found for Product Owner agent');
      }

      // Replace template variables with actual values
      const prompt = promptTemplate
        .replace(/\$\{userStory\.title\}/g, userStory.title)
        .replace(/\$\{userStory\.description\}/g, userStory.description)
        .replace(/\$\{userStory\.acceptance_criteria\}/g, userStory.acceptance_criteria)
        .replace(/\$\{userStory\.priority\}/g, userStory.priority)
        .replace(/\$\{userStory\.story_points\}/g, userStory.story_points)
        .replace(/\$\{commentsSummary\}/g, commentsSummary)
        .replace(/\$\{blockers\.length > 0 \? `IMPORTANT - Active Blockers Found:\n\$\{blockers\.map\(b => `- \$\{b\.comment_text\}`\)\.join\('\\n'\)\}\n\nPlease address these blockers in your refinement\.` : ''\}/g,
          blockers.length > 0 ? `IMPORTANT - Active Blockers Found:\n${blockers.map(b => `- ${b.comment_text}`).join('\n')}\n\nPlease address these blockers in your refinement.` : '');

      // Use intelligent completion for user story refinement
      const context = {
        userStory,
        projectId,
        commentsSummary,
        blockers: blockers.length,
        priority: userStory.priority,
        storyPoints: userStory.story_points
      };

      const result = await getUserStoryRefinementCompletion(prompt, context);
      const cleanResponse = result.content.trim().replace(/```json\n?/g, '').replace(/```\n?/g, '');
      const refinements = JSON.parse(cleanResponse);

      // Log model selection info
      if (result.metadata) {
        await this.commentManager.addComment(
          projectId,
          'user_story',
          userStory.id,
          this.agentName,
          `Used ${result.metadata.modelName} for refinement. ${result.metadata.reasoning}`,
          'update',
          {
            model: result.metadata.selectedModel,
            estimated_cost: result.metadata.estimatedCost,
            task_complexity: result.metadata.taskAnalysis?.complexity
          }
        );
      }

      // Log successful refinement
      await this.commentManager.logDecision(
        projectId,
        'user_story',
        userStory.id,
        this.agentName,
        'Story refinement completed',
        `Added ${refinements.edge_cases?.length || 0} edge cases, ${refinements.dependencies?.length || 0} dependencies, and ${refinements.definition_of_done?.length || 0} done criteria.`,
        {
          edge_cases_count: refinements.edge_cases?.length || 0,
          dependencies_count: refinements.dependencies?.length || 0,
          done_criteria_count: refinements.definition_of_done?.length || 0
        }
      );

      return {
        ...userStory,
        ...refinements,
        refined_by: 'ProductOwnerAgent',
        refined_at: new Date().toISOString()
      };
    } catch (error) {
      console.error('Product Owner refinement failed:', error);

      // Log error
      await this.commentManager.addComment(
        projectId,
        'user_story',
        userStory.id,
        this.agentName,
        `Refinement failed: ${error.message}`,
        'blocker',
        { error_type: error.name },
        { priority: 'critical' }
      );

      return userStory;
    }
  }
}

class ScrumMasterAgent {
  constructor(commentManager, manager) {
    this.commentManager = commentManager;
    this.manager = manager;
    this.agentName = 'ScrumMasterAgent';
  }

  async breakdownStory(userStory, projectId) {
    try {
      // Read existing comments first
      const commentsSummary = await this.commentManager.getCommentsSummary(
        projectId,
        'user_story',
        userStory.id
      );

      // Check for blockers
      const blockers = await this.commentManager.getBlockers(
        projectId,
        'user_story',
        userStory.id
      );

      // Log start of breakdown
      await this.commentManager.addComment(
        projectId,
        'user_story',
        userStory.id,
        this.agentName,
        'Starting task breakdown process. Analyzing story complexity and requirements.',
        'progress',
        { story_points: userStory.story_points }
      );

      // Load configurable prompt template
      const promptTemplate = await this.manager.loadPrompt(projectId, 'scrum_master');

      if (!promptTemplate) {
        throw new Error('No prompt template found for Scrum Master agent');
      }

      // Replace template variables with actual values
      const prompt = promptTemplate
        .replace(/\$\{userStory\.title\}/g, userStory.title)
        .replace(/\$\{userStory\.description\}/g, userStory.description)
        .replace(/\$\{userStory\.acceptance_criteria\}/g, userStory.acceptance_criteria)
        .replace(/\$\{userStory\.refined_acceptance_criteria \|\| 'Not refined yet'\}/g, userStory.refined_acceptance_criteria || 'Not refined yet')
        .replace(/\$\{JSON\.stringify\(userStory\.edge_cases \|\| \[\]\)\}/g, JSON.stringify(userStory.edge_cases || []))
        .replace(/\$\{JSON\.stringify\(userStory\.dependencies \|\| \[\]\)\}/g, JSON.stringify(userStory.dependencies || []))
        .replace(/\$\{JSON\.stringify\(userStory\.definition_of_done \|\| \[\]\)\}/g, JSON.stringify(userStory.definition_of_done || []))
        .replace(/\$\{commentsSummary\}/g, commentsSummary)
        .replace(/\$\{blockers\.length > 0 \? `IMPORTANT - Active Blockers:\n\$\{blockers\.map\(b => `- \$\{b\.comment_text\}`\)\.join\('\\n'\)\}\n\nConsider these blockers when creating tasks\.` : ''\}/g,
          blockers.length > 0 ? `IMPORTANT - Active Blockers:\n${blockers.map(b => `- ${b.comment_text}`).join('\n')}\n\nConsider these blockers when creating tasks.` : '');

      // Use intelligent completion for task breakdown
      const context = {
        userStory,
        projectId,
        commentsSummary,
        blockers: blockers.length,
        storyPoints: userStory.story_points,
        hasRefinements: !!(userStory.refined_acceptance_criteria || userStory.edge_cases || userStory.dependencies)
      };

      const result = await getTaskBreakdownCompletion(prompt, context);
      const cleanResponse = result.content.trim().replace(/```json\n?/g, '').replace(/```\n?/g, '');
      const breakdown = JSON.parse(cleanResponse);

      // Log model selection info
      if (result.metadata) {
        await this.commentManager.addComment(
          projectId,
          'user_story',
          userStory.id,
          this.agentName,
          `Used ${result.metadata.modelName} for task breakdown. ${result.metadata.reasoning}`,
          'update',
          {
            model: result.metadata.selectedModel,
            estimated_cost: result.metadata.estimatedCost,
            task_complexity: result.metadata.taskAnalysis?.complexity
          }
        );
      }

      const tasks = breakdown.tasks.map(task => ({
        id: nanoid(),
        ...task,
        story_id: userStory.id,
        status: 'todo',
        created_by: 'ScrumMasterAgent',
        created_at: new Date().toISOString()
      }));

      // Log successful breakdown with task details
      const totalHours = tasks.reduce((sum, task) => sum + (task.estimated_hours || 0), 0);
      await this.commentManager.logDecision(
        projectId,
        'user_story',
        userStory.id,
        this.agentName,
        'Task breakdown completed',
        `Created ${tasks.length} tasks with total estimated effort of ${totalHours} hours. Tasks include: ${tasks.map(t => t.title).join(', ')}`,
        {
          task_count: tasks.length,
          total_estimated_hours: totalHours,
          task_types: [...new Set(tasks.map(t => t.type))]
        }
      );

      // Save tasks to database
      await this.saveTasks(tasks, projectId, userStory.id);

      // Add comments for each task created
      for (const task of tasks) {
        await this.commentManager.addComment(
          projectId,
          'task',
          task.id,
          this.agentName,
          `Task created from user story: ${userStory.title}. Estimated effort: ${task.estimated_hours} hours.`,
          'progress',
          {
            parent_story_id: userStory.id,
            estimated_hours: task.estimated_hours,
            task_type: task.type,
            priority: task.priority
          }
        );
      }

      return tasks;
    } catch (error) {
      console.error('Scrum Master breakdown failed:', error);

      // Log error
      await this.commentManager.addComment(
        projectId,
        'user_story',
        userStory.id,
        this.agentName,
        `Task breakdown failed: ${error.message}`,
        'blocker',
        { error_type: error.name },
        { priority: 'critical' }
      );

      return [];
    }
  }

  async saveTasks(tasks, projectId, storyId) {
    const db = require('./database');

    return new Promise((resolve, reject) => {
      let completed = 0;
      let errors = [];

      if (tasks.length === 0) {
        resolve();
        return;
      }

      for (const task of tasks) {
        db.run(`INSERT INTO tasks (
          id, title, description, project_id, status, type,
          estimated_hours, priority, assigned_to, story_id,
          created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`, [
          task.id,
          task.title,
          task.description || '',
          projectId,
          task.status || 'todo',
          task.type || 'development',
          task.estimated_hours || 0,
          task.priority || 'medium',
          task.assigned_to || 'AI-Developer',
          storyId,
          new Date().toISOString(),
          new Date().toISOString()
        ], function(err) {
          if (err) {
            errors.push(err);
          }
          completed++;

          if (completed === tasks.length) {
            if (errors.length > 0) {
              console.error('Some tasks failed to save:', errors);
              reject(errors[0]);
            } else {
              console.log(`✅ Saved ${tasks.length} tasks to database`);
              resolve();
            }
          }
        });
      }
    });
  }

  async generateProgressReport(projectId, userStory, tasks) {
    const report = `# AI Development Progress Report

## Project: ${projectId}
## Generated: ${new Date().toISOString()}

### User Story Processed
**Title:** ${userStory.title}
**Priority:** ${userStory.priority}
**Story Points:** ${userStory.story_points}

### Tasks Generated
${tasks.map(task => `
- **${task.title}** (${task.type})
  - Estimated Hours: ${task.estimated_hours}
  - Priority: ${task.priority}
  - Status: ${task.status}
`).join('')}

### Next Steps
1. Review generated code structure
2. Run initial tests
3. Validate against acceptance criteria
4. Deploy to development environment

### AI Agents Involved
- Product Owner AI: Story refinement
- Scrum Master AI: Task breakdown and reporting
- Lead Developer AI: Code generation and architecture

---
*Report generated by Quarrel AI Development Team*
`;

    return report;
  }

  async generateSprintReport(projectId, sprintId, processedStories) {
    const report = `# Sprint Completion Report

## Project: ${projectId}
## Sprint: ${sprintId}
## Generated: ${new Date().toISOString()}

### Sprint Summary
- **Stories Processed**: ${processedStories.length}
- **Total Files Generated**: ${processedStories.reduce((sum, story) => sum + (story.codeStructure?.files?.length || 0), 0)}
- **Total Tasks Created**: ${processedStories.reduce((sum, story) => sum + (story.tasks?.length || 0), 0)}

### Stories Completed
${processedStories.map(story => `
#### ${story.refinedStory.title}
- **Priority**: ${story.refinedStory.priority}
- **Story Points**: ${story.refinedStory.story_points}
- **Files Generated**: ${story.codeStructure?.files?.length || 0}
- **Tasks Created**: ${story.tasks?.length || 0}
`).join('')}

### Development Artifacts
${processedStories.map(story =>
  story.codeStructure?.files?.map(file => `- ${file.path}: ${file.description}`).join('\n') || ''
).join('\n')}

### Next Sprint Recommendations
1. Review and test implemented features
2. Gather user feedback on completed stories
3. Plan next iteration based on sprint review
4. Address any technical debt identified

### AI Team Performance
- **Code Generation**: Successfully generated working code for all assigned stories
- **Task Breakdown**: Created detailed development tasks with time estimates
- **Documentation**: Generated comprehensive reports and documentation

---
*Sprint report generated by Quarrel AI Development Team*
`;

    return report;
  }
}

class LeadDeveloperAgent {
  constructor(commentManager, manager) {
    this.commentManager = commentManager;
    this.manager = manager;
    this.agentName = 'LeadDeveloperAgent';
  }

  async assignTasks(tasks, projectId, storyId) {
    try {
      // Read comments for the user story to understand context
      const commentsSummary = await this.commentManager.getCommentsSummary(
        projectId,
        'user_story',
        storyId
      );

      // Log start of assignment process
      await this.commentManager.addComment(
        projectId,
        'user_story',
        storyId,
        this.agentName,
        `Starting task assignment for ${tasks.length} tasks. Analyzing task complexity and dependencies.`,
        'progress',
        { task_count: tasks.length }
      );

      // For now, assign all tasks to AI developers
      const assignedTasks = tasks.map(task => ({
        ...task,
        assigned_to: 'AI-Developer',
        assigned_at: new Date().toISOString()
      }));

      // Log assignment decisions
      await this.commentManager.logDecision(
        projectId,
        'user_story',
        storyId,
        this.agentName,
        'Task assignment completed',
        `Assigned ${assignedTasks.length} tasks to AI developers. Task distribution: ${assignedTasks.map(t => `${t.title} (${t.estimated_hours}h)`).join(', ')}`,
        {
          assigned_tasks: assignedTasks.length,
          total_hours: assignedTasks.reduce((sum, t) => sum + (t.estimated_hours || 0), 0)
        }
      );

      return assignedTasks;
    } catch (error) {
      console.error('Lead Developer assignment failed:', error);

      // Log error
      await this.commentManager.addComment(
        projectId,
        'user_story',
        storyId,
        this.agentName,
        `Task assignment failed: ${error.message}`,
        'blocker',
        { error_type: error.name },
        { priority: 'critical' }
      );

      return tasks;
    }
  }

  async generateCodeStructure(userStory, projectId) {
    try {
      // Get project details including tech stack
      const project = await this.getProjectDetails(projectId);
      const techStackInfo = this.getTechStackInfo(project.tech_stack);

      // Read comments for context
      const commentsSummary = await this.commentManager.getCommentsSummary(
        projectId,
        'user_story',
        userStory.id
      );

      // Log start of code generation
      await this.commentManager.addComment(
        projectId,
        'user_story',
        userStory.id,
        this.agentName,
        `Starting code structure generation for ${techStackInfo.name}. Analyzing requirements and technical approach.`,
        'progress'
      );

      // Load configurable prompt template
      const promptTemplate = await this.manager.loadPrompt(projectId, 'lead_developer');

      if (!promptTemplate) {
        throw new Error('No prompt template found for Lead Developer agent');
      }

      // Helper function to safely escape text for prompt replacement
      const escapeForPrompt = (text) => {
        if (!text || typeof text !== 'string') return '';
        return text.replace(/[\x00-\x1F\x7F]/g, ' ').replace(/\s+/g, ' ').trim();
      };

      // Replace template variables with actual values
      const prompt = promptTemplate
        .replace(/\$\{userStory\.title\}/g, escapeForPrompt(userStory.title))
        .replace(/\$\{userStory\.description\}/g, escapeForPrompt(userStory.description))
        .replace(/\$\{userStory\.refined_acceptance_criteria \|\| 'Not refined yet'\}/g, escapeForPrompt(userStory.refined_acceptance_criteria) || 'Not refined yet')
        .replace(/\$\{JSON\.stringify\(userStory\.edge_cases \|\| \[\]\)\}/g, JSON.stringify(userStory.edge_cases || []))
        .replace(/\$\{JSON\.stringify\(userStory\.dependencies \|\| \[\]\)\}/g, JSON.stringify(userStory.dependencies || []))
        .replace(/\$\{JSON\.stringify\(userStory\.definition_of_done \|\| \[\]\)\}/g, JSON.stringify(userStory.definition_of_done || []))
        .replace(/\$\{techStackInfo\.name\}/g, techStackInfo.name)
        .replace(/\$\{techStackInfo\.requirements\}/g, techStackInfo.requirements)
        .replace(/\$\{commentsSummary\}/g, escapeForPrompt(commentsSummary))
        .replace(/\$\{techStackInfo\.examples\}/g, techStackInfo.examples);

      // Use intelligent completion for code generation
      const context = {
        userStory,
        projectId,
        techStack: project.tech_stack,
        commentsSummary,
        hasRefinements: !!(userStory.refined_acceptance_criteria || userStory.edge_cases || userStory.dependencies),
        complexity: userStory.story_points || 3
      };

      const result = await getCodeGenerationCompletion(prompt, context);

      // Log model selection info
      if (result.metadata) {
        await this.commentManager.addComment(
          projectId,
          'user_story',
          userStory.id,
          this.agentName,
          `Used ${result.metadata.modelName} for code generation. ${result.metadata.reasoning}`,
          'update',
          {
            model: result.metadata.selectedModel,
            estimated_cost: result.metadata.estimatedCost,
            task_complexity: result.metadata.taskAnalysis?.complexity
          }
        );
      }

      let structure;
      try {
        // Use robust JSON extraction
        structure = this.extractJsonFromResponse(result.content);
      } catch (parseError) {
        console.error('❌ JSON parsing failed:', parseError.message);
        console.error('📄 Raw response:', result.content.substring(0, 200) + '...');

        // Fallback: create a basic structure
        structure = {
          files: [],
          dependencies: [],
          notes: `Code generation failed due to JSON parsing error: ${parseError.message}`
        };
      }

      // Log successful code generation
      await this.commentManager.logDecision(
        projectId,
        'user_story',
        userStory.id,
        this.agentName,
        'Code structure generation completed',
        `Generated ${structure.files?.length || 0} files: ${structure.files?.map(f => f.path).join(', ')}`,
        {
          files_generated: structure.files?.length || 0,
          file_paths: structure.files?.map(f => f.path) || []
        }
      );

      return {
        ...structure,
        generated_by: 'LeadDeveloperAgent',
        generated_at: new Date().toISOString()
      };
    } catch (error) {
      console.error('Lead Developer code generation failed:', error);

      // Log error
      await this.commentManager.addComment(
        projectId,
        'user_story',
        userStory.id,
        this.agentName,
        `Code generation failed: ${error.message}`,
        'blocker',
        { error_type: error.name },
        { priority: 'critical' }
      );

      return { files: [] };
    }
  }

  async getProjectDetails(projectId) {
    const db = require('./database');
    return new Promise((resolve, reject) => {
      db.get('SELECT * FROM projects WHERE id = ?', [projectId], (err, row) => {
        if (err) reject(err);
        else resolve(row);
      });
    });
  }

  getTechStackInfo(techStack) {
    const techStacks = {
      'rust': {
        name: 'Rust + Actix/Warp',
        requirements: `
- Use Rust with either Actix-web or Warp framework
- Include proper Cargo.toml with all dependencies
- Use Serde for JSON serialization
- Include proper error handling with Result types
- Use async/await patterns
- Include database connectivity (SQLx or Diesel)
- Add CORS middleware for frontend integration
- Follow Rust naming conventions (snake_case)
- Include proper module structure`,
        examples: `
Example Cargo.toml:
[package]
name = "calendar-notes-api"
version = "0.1.0"
edition = "2021"

[dependencies]
actix-web = "4.0"
tokio = { version = "1.0", features = ["full"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
sqlx = { version = "0.7", features = ["runtime-tokio-rustls", "sqlite"] }
actix-cors = "0.6"
chrono = { version = "0.4", features = ["serde"] }
uuid = { version = "1.0", features = ["v4", "serde"] }

Example main.rs structure:
use actix_web::{web, App, HttpServer, Result};
use actix_cors::Cors;

#[actix_web::main]
async fn main() -> std::io::Result<()> {
    HttpServer::new(|| {
        App::new()
            .wrap(Cors::permissive())
            .route("/health", web::get().to(health_check))
    })
    .bind("127.0.0.1:8080")?
    .run()
    .await
}`
      },
      'react': {
        name: 'React + Node.js',
        requirements: `
- Use React with functional components and hooks
- Use Express.js for backend API
- Include proper package.json files
- Use modern JavaScript/TypeScript
- Include proper error handling
- Use RESTful API design
- Include CORS configuration`,
        examples: `
Example package.json:
{
  "name": "app",
  "version": "1.0.0",
  "dependencies": {
    "express": "^4.18.0",
    "cors": "^2.8.5"
  }
}`
      }
    };

    return techStacks[techStack] || techStacks['react'];
  }

  /**
   * Extract JSON from AI response using robust parsing
   * @param {string} response - Raw AI response
   * @returns {Object} Parsed JSON object
   */
  extractJsonFromResponse(response) {
    let jsonString = null;

    // Method 1: Look for JSON between code blocks
    const codeBlockMatch = response.match(/```(?:json)?\s*(\{[\s\S]*?\})\s*```/);
    if (codeBlockMatch) {
      jsonString = codeBlockMatch[1];
    } else {
      // Method 2: Find the first complete JSON object
      const startIndex = response.indexOf('{');
      if (startIndex !== -1) {
        let braceCount = 0;
        let endIndex = startIndex;

        for (let i = startIndex; i < response.length; i++) {
          if (response[i] === '{') braceCount++;
          if (response[i] === '}') braceCount--;
          if (braceCount === 0) {
            endIndex = i;
            break;
          }
        }

        if (braceCount === 0) {
          jsonString = response.substring(startIndex, endIndex + 1);
        }
      }
    }

    if (!jsonString) {
      throw new Error('No valid JSON found in response');
    }

    // Clean up any control characters that might break JSON parsing
    const sanitizedJson = jsonString.replace(/[\x00-\x1F\x7F]/g, '');

    return JSON.parse(sanitizedJson);
  }
}

module.exports = { AIAgentManager };
