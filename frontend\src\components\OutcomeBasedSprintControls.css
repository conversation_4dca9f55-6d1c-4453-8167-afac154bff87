.outcome-based-controls {
  background: #1f1f1f;
  border: 1px solid #404040;
  border-radius: 8px;
  padding: 20px;
  margin: 16px 0;
}

/* Completion Check */
.completion-check {
  margin-bottom: 24px;
}

.check-header {
  display: flex;
  justify-content: between;
  align-items: center;
  margin-bottom: 16px;
}

.check-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #e5e5e5;
}

.status-icon {
  width: 20px;
  height: 20px;
}

.status-icon.success {
  color: #10b981;
}

.status-icon.warning {
  color: #f59e0b;
}

.status-icon.neutral {
  color: #6b7280;
}

.refresh-button {
  background: #374151;
  color: #d1d5db;
  border: 1px solid #4b5563;
  border-radius: 6px;
  padding: 6px 12px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.refresh-button:hover:not(:disabled) {
  background: #4b5563;
  border-color: #6b7280;
}

.refresh-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Completion Status */
.completion-status {
  border-radius: 6px;
  padding: 16px;
  border-left: 4px solid;
}

.completion-status.success {
  background: rgba(16, 185, 129, 0.1);
  border-left-color: #10b981;
}

.completion-status.warning {
  background: rgba(245, 158, 11, 0.1);
  border-left-color: #f59e0b;
}

.completion-status.neutral {
  background: rgba(107, 114, 128, 0.1);
  border-left-color: #6b7280;
}

.status-message {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
  font-size: 14px;
  color: #d1d5db;
}

.message-icon {
  width: 16px;
  height: 16px;
  flex-shrink: 0;
}

/* Completion Metrics */
.completion-metrics {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 16px;
  margin-bottom: 16px;
}

.metric {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.metric-label {
  font-size: 12px;
  color: #9ca3af;
  text-transform: uppercase;
  font-weight: 500;
}

.metric-value {
  font-size: 18px;
  font-weight: 600;
  color: #e5e5e5;
}

/* Complete Sprint Button */
.complete-sprint-button {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  border: none;
  border-radius: 6px;
  padding: 10px 20px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  width: 100%;
}

.complete-sprint-button:hover:not(:disabled) {
  background: linear-gradient(135deg, #059669, #047857);
  transform: translateY(-1px);
}

.complete-sprint-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

/* Scope Suggestion */
.scope-suggestion {
  margin-bottom: 24px;
}

.suggestion-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
  font-weight: 600;
  color: #e5e5e5;
}

.suggestion-icon {
  width: 20px;
  height: 20px;
  color: #3b82f6;
}

.suggestion-content {
  background: rgba(59, 130, 246, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 6px;
  padding: 16px;
}

.suggestion-metrics {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 16px;
  margin-bottom: 16px;
}

.suggestion-reason {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.confidence {
  font-size: 11px;
  font-weight: 700;
  padding: 4px 8px;
  border-radius: 4px;
  width: fit-content;
}

.confidence.high {
  background: rgba(16, 185, 129, 0.2);
  color: #6ee7b7;
}

.confidence.medium {
  background: rgba(245, 158, 11, 0.2);
  color: #fbbf24;
}

.confidence.low {
  background: rgba(239, 68, 68, 0.2);
  color: #fca5a5;
}

.reason-text {
  font-size: 14px;
  color: #d1d5db;
  line-height: 1.4;
}

/* Flow Principles Reminder */
.flow-principles-reminder {
  border-top: 1px solid #404040;
  padding-top: 16px;
}

.principle {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-size: 13px;
  color: #9ca3af;
}

.principle:last-child {
  margin-bottom: 0;
}

.principle-icon {
  width: 14px;
  height: 14px;
  color: #60a5fa;
  flex-shrink: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .outcome-based-controls {
    padding: 16px;
    margin: 12px 0;
  }
  
  .completion-metrics,
  .suggestion-metrics {
    grid-template-columns: 1fr;
    gap: 12px;
  }
  
  .check-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .refresh-button {
    align-self: stretch;
  }
}
