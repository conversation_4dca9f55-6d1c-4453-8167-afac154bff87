import React, { useState, useEffect } from 'react';
import ConfirmationModal from './ConfirmationModal';
import CountdownTimer from './CountdownTimer';
import { Bot, Clock, AlertCircle, CheckCircle, Loader } from 'lucide-react';
import './AISchedulerControl.css';

const AISchedulerControl = () => {
  const [schedulerStatus, setSchedulerStatus] = useState(null);
  const [loading, setLoading] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [showErrorModal, setShowErrorModal] = useState(false);
  const [modalMessage, setModalMessage] = useState('');
  const [activeProjectsWithWork, setActiveProjectsWithWork] = useState([]);

  useEffect(() => {
    fetchSchedulerStatus();

    // Refresh status every 10 seconds
    const interval = setInterval(fetchSchedulerStatus, 10000);
    return () => clearInterval(interval);
  }, []);

  const fetchSchedulerStatus = async () => {
    try {
      const response = await fetch('/api/ai/scheduler/status');
      const data = await response.json();
      setSchedulerStatus(data);



      // Update active projects with work from scheduler status
      if (data.activeProjectsWithWork) {
        setActiveProjectsWithWork(data.activeProjectsWithWork);
      }
    } catch (error) {
      console.error('Error fetching scheduler status:', error);
    }
  };



  const startScheduler = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/ai/scheduler/start', {
        method: 'POST'
      });
      
      if (response.ok) {
        await fetchSchedulerStatus();
        setModalMessage('AI Scheduler started successfully!');
        setShowSuccessModal(true);
      } else {
        const error = await response.json();
        setModalMessage(`Failed to start scheduler: ${error.error}`);
        setShowErrorModal(true);
      }
    } catch (error) {
      console.error('Error starting scheduler:', error);
      setModalMessage('Error starting scheduler');
      setShowErrorModal(true);
    } finally {
      setLoading(false);
    }
  };

  const stopScheduler = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/ai/scheduler/stop', {
        method: 'POST'
      });
      
      if (response.ok) {
        await fetchSchedulerStatus();
        setModalMessage('AI Scheduler stopped successfully!');
        setShowSuccessModal(true);
      } else {
        const error = await response.json();
        setModalMessage(`Failed to stop scheduler: ${error.error}`);
        setShowErrorModal(true);
      }
    } catch (error) {
      console.error('Error stopping scheduler:', error);
      setModalMessage('Error stopping scheduler');
      setShowErrorModal(true);
    } finally {
      setLoading(false);
    }
  };



  const formatUptime = (ms) => {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) return `${hours}h ${minutes % 60}m`;
    if (minutes > 0) return `${minutes}m ${seconds % 60}s`;
    return `${seconds}s`;
  };

  const getStatusColor = (isRunning) => {
    return isRunning ? '#4CAF50' : '#f44336';
  };

  const getPriorityIcon = (priority) => {
    switch (priority) {
      case 'critical':
        return <AlertCircle size={16} className="priority-critical" />;
      case 'high':
        return <AlertCircle size={16} className="priority-high" />;
      case 'medium':
        return <Clock size={16} className="priority-medium" />;
      case 'low':
        return <CheckCircle size={16} className="priority-low" />;
      default:
        return <Clock size={16} className="priority-medium" />;
    }
  };

  const formatTimeAgo = (timestamp) => {
    const now = new Date();
    const time = new Date(timestamp);
    const diffMs = now - time;
    const diffMins = Math.floor(diffMs / (1000 * 60));

    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;

    const diffHours = Math.floor(diffMins / 60);
    if (diffHours < 24) return `${diffHours}h ago`;

    const diffDays = Math.floor(diffHours / 24);
    return `${diffDays}d ago`;
  };

  const getTotalActiveWorkCount = (project) => {
    return project.activeSprints.reduce((total, sprint) => total + sprint.activeItems.length, 0);
  };

  const getProjectWorkStatus = (project) => {
    const totalWork = getTotalActiveWorkCount(project);
    return totalWork > 0 ? 'active' : 'idle';
  };

  if (!schedulerStatus) {
    return <div className="ai-scheduler-loading">Loading AI Scheduler status...</div>;
  }

  return (
    <div className="ai-scheduler-control">
      <div className="scheduler-header">
        <h2>AI Processing Scheduler</h2>
        <div className="scheduler-status">
          <div
            className="status-indicator"
            style={{ backgroundColor: getStatusColor(schedulerStatus.isRunning) }}
          ></div>
          <span className="status-text">
            {schedulerStatus.isRunning ? 'Running' : 'Stopped'}
          </span>
        </div>
      </div>

      {/* Hero Countdown Timer */}
      <CountdownTimer
        targetTime={schedulerStatus.nextTickTime}
        isRunning={schedulerStatus.isRunning}
        tickRate={schedulerStatus.config?.tickRate || 3600000}
      />

      <div className="scheduler-content">
        {/* Statistics Section - Full Width & Stylish */}
        <div className="stats-section-full">
          <h3>AI Scheduler Statistics</h3>
          <div className="stats-grid">
            <div className="stat-card-modern">
              <div className="stat-icon">
                <div className="icon-circle blue">
                  <span>⚡</span>
                </div>
              </div>
              <div className="stat-content">
                <div className="stat-value-large">{schedulerStatus.stats.totalTicks}</div>
                <div className="stat-label-modern">Total Ticks</div>
              </div>
            </div>

            <div className="stat-card-modern">
              <div className="stat-icon">
                <div className="icon-circle green">
                  <span>✓</span>
                </div>
              </div>
              <div className="stat-content">
                <div className="stat-value-large">{schedulerStatus.stats.storiesProcessed}</div>
                <div className="stat-label-modern">Stories Processed</div>
              </div>
            </div>

            <div className="stat-card-modern">
              <div className="stat-icon">
                <div className="icon-circle red">
                  <span>⚠</span>
                </div>
              </div>
              <div className="stat-content">
                <div className="stat-value-large">{schedulerStatus.stats.errors}</div>
                <div className="stat-label-modern">Errors</div>
              </div>
            </div>

            <div className="stat-card-modern">
              <div className="stat-icon">
                <div className="icon-circle orange">
                  <span>⏸</span>
                </div>
              </div>
              <div className="stat-content">
                <div className="stat-value-large">{schedulerStatus.stats.skippedTicks}</div>
                <div className="stat-label-modern">Skipped Ticks</div>
              </div>
            </div>

            {schedulerStatus.stats.lastActivity && (
              <div className="stat-card-modern last-activity">
                <div className="stat-icon">
                  <div className="icon-circle purple">
                    <span>🕒</span>
                  </div>
                </div>
                <div className="stat-content">
                  <div className="stat-value-time">
                    {new Date(schedulerStatus.stats.lastActivity).toLocaleString()}
                  </div>
                  <div className="stat-label-modern">Last Activity</div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Active Projects with Sprints and AI Work */}
        <div className="projects-section">
          <h3>Active Projects, Sprints & AI Work ({activeProjectsWithWork.length})</h3>
          <div className="projects-content">
            {activeProjectsWithWork.length > 0 ? (
              activeProjectsWithWork.map(project => {
                const totalWork = getTotalActiveWorkCount(project);
                const workStatus = getProjectWorkStatus(project);

                return (
                  <div key={project.projectId} className="project-card">
                    <div className="project-header">
                      <div className="project-info">
                        <span className="project-name">{project.projectName}</span>
                        <span className={`project-status ${project.aiProcessingEnabled ? 'enabled' : 'disabled'}`}>
                          {project.aiProcessingEnabled ? 'AI Enabled' : 'AI Disabled'}
                        </span>
                      </div>
                      <div className="project-work-indicator">
                        {workStatus === 'active' ? (
                          <div className="work-active">
                            <Loader size={16} className="spinning" />
                            <span>{totalWork} active</span>
                          </div>
                        ) : (
                          <div className="work-idle">
                            <Bot size={16} />
                            <span>Idle</span>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Active Sprints */}
                    {project.activeSprints.length > 0 && (
                      <div className="sprints-container">
                        {project.activeSprints.map(sprint => (
                          <div key={sprint.sprintId} className="sprint-card">
                            <div className="sprint-header">
                              <div className="sprint-info">
                                <span className="sprint-name">{sprint.sprintName}</span>
                                {sprint.sprintStartDate && sprint.sprintEndDate && (
                                  <span className="sprint-dates">
                                    {sprint.sprintStartDate} - {sprint.sprintEndDate}
                                  </span>
                                )}
                              </div>
                              <div className="sprint-work-indicator">
                                {sprint.activeItems.length > 0 ? (
                                  <div className="work-active">
                                    <Loader size={14} className="spinning" />
                                    <span>{sprint.activeItems.length} active</span>
                                  </div>
                                ) : (
                                  <div className="work-idle">
                                    <CheckCircle size={14} />
                                    <span>No active work</span>
                                  </div>
                                )}
                              </div>
                            </div>

                            {/* AI Work Items for this Sprint */}
                            {sprint.activeItems.length > 0 && (
                              <div className="ai-work-items">
                                {sprint.activeItems.map(item => (
                                  <div key={item.queueId} className="work-item">
                                    <div className="work-item-header">
                                      <div className="work-item-info">
                                        {getPriorityIcon(item.priority)}
                                        <span className="work-item-title">{item.title}</span>
                                        <span className="work-item-type">{item.entityType}</span>
                                      </div>
                                      <span className="work-item-time">
                                        {formatTimeAgo(item.startedAt)}
                                      </span>
                                    </div>
                                    {item.description && (
                                      <div className="work-item-description">
                                        {item.description.length > 100
                                          ? `${item.description.substring(0, 100)}...`
                                          : item.description}
                                      </div>
                                    )}
                                  </div>
                                ))}
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                );
              })
            ) : (
              <div className="empty-state">No active projects with AI enabled</div>
            )}
          </div>
        </div>


      </div>

      <div className="scheduler-controls">
        <div className="control-buttons">
          {schedulerStatus.isRunning ? (
            <button 
              className="btn btn-danger"
              onClick={stopScheduler}
              disabled={loading}
            >
              {loading ? 'Stopping...' : 'Stop Scheduler'}
            </button>
          ) : (
            <button 
              className="btn btn-success"
              onClick={startScheduler}
              disabled={loading}
            >
              {loading ? 'Starting...' : 'Start Scheduler'}
            </button>
          )}
        </div>

        {/* Configuration Note */}
        <div className="config-note">
          <p>
            <strong>Configuration:</strong> To modify scheduler settings (tick rate, guardrails, etc.),
            go to <strong>Settings → AI Scheduler</strong> tab.
          </p>
        </div>
      </div>

      {/* Success Modal */}
      <ConfirmationModal
        isOpen={showSuccessModal}
        onClose={() => setShowSuccessModal(false)}
        onConfirm={() => setShowSuccessModal(false)}
        title="Success"
        message={modalMessage}
        confirmText="OK"
        cancelText=""
        type="default"
      />

      {/* Error Modal */}
      <ConfirmationModal
        isOpen={showErrorModal}
        onClose={() => setShowErrorModal(false)}
        onConfirm={() => setShowErrorModal(false)}
        title="Error"
        message={modalMessage}
        confirmText="OK"
        cancelText=""
        type="danger"
      />
    </div>
  );
};

export default AISchedulerControl;
