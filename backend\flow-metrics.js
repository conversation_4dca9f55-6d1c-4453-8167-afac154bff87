/**
 * Flow Metrics Utility
 * Replaces time-based estimation with complexity scoring and flow-based metrics
 */

const db = require('./database');

// Complexity scoring system (Fibonacci-based)
const COMPLEXITY_SCORES = {
  'XS': 1,   // Trivial changes, config updates
  'S': 2,    // Small features, bug fixes
  'M': 3,    // Medium features (default)
  'L': 5,    // Large features, complex logic
  'XL': 8,   // Very large features, major refactoring
  'XXL': 13  // Epic-level work, should be broken down
};

const COMPLEXITY_LABELS = {
  1: 'XS',
  2: 'S', 
  3: 'M',
  5: 'L',
  8: 'XL',
  13: 'XXL'
};

/**
 * Calculate lead time (from story creation to completion)
 */
function calculateLeadTime(createdAt, completedAt) {
  if (!createdAt || !completedAt) return 0;
  
  const created = new Date(createdAt);
  const completed = new Date(completedAt);
  const diffMs = completed - created;
  
  return Math.round(diffMs / (1000 * 60 * 60)); // Convert to hours
}

/**
 * Calculate cycle time (from in-progress to done)
 */
function calculateCycleTime(startedAt, completedAt) {
  if (!startedAt || !completedAt) return 0;
  
  const started = new Date(startedAt);
  const completed = new Date(completedAt);
  const diffMs = completed - started;
  
  return Math.round(diffMs / (1000 * 60 * 60)); // Convert to hours
}

/**
 * Update story flow metrics when status changes
 */
async function updateStoryFlowMetrics(storyId, newStatus, oldStatus) {
  const now = new Date().toISOString();
  
  try {
    if (newStatus === 'in_progress' && oldStatus !== 'in_progress') {
      // Story started - record started_at timestamp
      await new Promise((resolve, reject) => {
        db.run(
          `UPDATE user_stories SET started_at = ?, updated_at = ? WHERE id = ?`,
          [now, now, storyId],
          function(err) {
            if (err) reject(err);
            else resolve();
          }
        );
      });
    }
    
    if (newStatus === 'done' && oldStatus !== 'done') {
      // Story completed - record completed_at and calculate metrics
      const story = await getStoryById(storyId);
      if (story) {
        const leadTime = calculateLeadTime(story.created_at, now);
        const cycleTime = calculateCycleTime(story.started_at, now);
        
        await new Promise((resolve, reject) => {
          db.run(
            `UPDATE user_stories SET 
             completed_at = ?, 
             lead_time_hours = ?, 
             cycle_time_hours = ?,
             updated_at = ?
             WHERE id = ?`,
            [now, leadTime, cycleTime, now, storyId],
            function(err) {
              if (err) reject(err);
              else resolve();
            }
          );
        });
        
        // Update sprint metrics if story is in a sprint
        if (story.sprint_id) {
          await updateSprintFlowMetrics(story.sprint_id);
        }
      }
    }
  } catch (error) {
    console.error('Error updating story flow metrics:', error);
  }
}

/**
 * Get story by ID
 */
async function getStoryById(storyId) {
  return new Promise((resolve, reject) => {
    db.get(
      `SELECT * FROM user_stories WHERE id = ?`,
      [storyId],
      (err, row) => {
        if (err) reject(err);
        else resolve(row);
      }
    );
  });
}

/**
 * Update sprint flow metrics
 */
async function updateSprintFlowMetrics(sprintId) {
  try {
    // Get all stories in the sprint
    const stories = await new Promise((resolve, reject) => {
      db.all(
        `SELECT * FROM user_stories WHERE sprint_id = ?`,
        [sprintId],
        (err, rows) => {
          if (err) reject(err);
          else resolve(rows || []);
        }
      );
    });
    
    const completedStories = stories.filter(s => s.status === 'done');
    const totalComplexity = stories.reduce((sum, s) => sum + (s.complexity_score || 3), 0);
    const completedComplexity = completedStories.reduce((sum, s) => sum + (s.complexity_score || 3), 0);
    
    // Calculate average lead and cycle times
    const storiesWithLeadTime = completedStories.filter(s => s.lead_time_hours > 0);
    const storiesWithCycleTime = completedStories.filter(s => s.cycle_time_hours > 0);
    
    const avgLeadTime = storiesWithLeadTime.length > 0 
      ? storiesWithLeadTime.reduce((sum, s) => sum + s.lead_time_hours, 0) / storiesWithLeadTime.length
      : 0;
      
    const avgCycleTime = storiesWithCycleTime.length > 0
      ? storiesWithCycleTime.reduce((sum, s) => sum + s.cycle_time_hours, 0) / storiesWithCycleTime.length
      : 0;
    
    // Calculate throughput (stories per day)
    const sprint = await getSprintById(sprintId);
    let throughputPerDay = 0;
    
    if (sprint && sprint.start_date) {
      const startDate = new Date(sprint.start_date);
      const now = new Date();
      const daysElapsed = Math.max(1, Math.ceil((now - startDate) / (1000 * 60 * 60 * 24)));
      throughputPerDay = completedStories.length / daysElapsed;
    }
    
    // Update sprint metrics
    await new Promise((resolve, reject) => {
      db.run(
        `UPDATE sprints SET 
         stories_completed = ?,
         stories_total = ?,
         complexity_completed = ?,
         complexity_total = ?,
         average_lead_time = ?,
         average_cycle_time = ?,
         throughput_per_day = ?,
         updated_at = CURRENT_TIMESTAMP
         WHERE id = ?`,
        [
          completedStories.length,
          stories.length,
          completedComplexity,
          totalComplexity,
          avgLeadTime,
          avgCycleTime,
          throughputPerDay,
          sprintId
        ],
        function(err) {
          if (err) reject(err);
          else resolve();
        }
      );
    });
    
  } catch (error) {
    console.error('Error updating sprint flow metrics:', error);
  }
}

/**
 * Get sprint by ID
 */
async function getSprintById(sprintId) {
  return new Promise((resolve, reject) => {
    db.get(
      `SELECT * FROM sprints WHERE id = ?`,
      [sprintId],
      (err, row) => {
        if (err) reject(err);
        else resolve(row);
      }
    );
  });
}

/**
 * Calculate project velocity (complexity delivered per sprint)
 */
async function calculateProjectVelocity(projectId, sprintCount = 3) {
  try {
    const sprints = await new Promise((resolve, reject) => {
      db.all(
        `SELECT complexity_completed, start_date, end_date 
         FROM sprints 
         WHERE project_id = ? AND status = 'completed'
         ORDER BY end_date DESC 
         LIMIT ?`,
        [projectId, sprintCount],
        (err, rows) => {
          if (err) reject(err);
          else resolve(rows || []);
        }
      );
    });
    
    if (sprints.length === 0) return 0;
    
    const totalComplexity = sprints.reduce((sum, s) => sum + (s.complexity_completed || 0), 0);
    return Math.round(totalComplexity / sprints.length);
    
  } catch (error) {
    console.error('Error calculating project velocity:', error);
    return 0;
  }
}

/**
 * Record flow metric in history table
 */
async function recordFlowMetric(projectId, sprintId, metricType, metricValue, additionalData = {}) {
  const { nanoid } = await import('nanoid');
  
  try {
    await new Promise((resolve, reject) => {
      db.run(
        `INSERT INTO flow_metrics (
          id, project_id, sprint_id, metric_type, metric_value,
          complexity_delivered, stories_delivered, measurement_date,
          period_start, period_end, metadata
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          nanoid(),
          projectId,
          sprintId,
          metricType,
          metricValue,
          additionalData.complexity_delivered || 0,
          additionalData.stories_delivered || 0,
          new Date().toISOString().split('T')[0],
          additionalData.period_start || null,
          additionalData.period_end || null,
          JSON.stringify(additionalData.metadata || {})
        ],
        function(err) {
          if (err) reject(err);
          else resolve();
        }
      );
    });
  } catch (error) {
    console.error('Error recording flow metric:', error);
  }
}

module.exports = {
  COMPLEXITY_SCORES,
  COMPLEXITY_LABELS,
  calculateLeadTime,
  calculateCycleTime,
  updateStoryFlowMetrics,
  updateSprintFlowMetrics,
  calculateProjectVelocity,
  recordFlowMetric,
  getStoryById,
  getSprintById
};
