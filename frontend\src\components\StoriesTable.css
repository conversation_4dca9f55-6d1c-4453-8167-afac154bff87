.stories-table-container {
  flex: 1;
  background: #1a1a1a;
  color: #e5e5e5;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  border-bottom: 1px solid #2d2d2d;
  background: #1a1a1a;
  min-height: 60px;
  width: 100%;
  box-sizing: border-box;
}

.header-title {
  flex: 0 0 auto;
}

.header-title h2 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #ffffff;
  line-height: 1;
}

.table-actions {
  display: flex;
  gap: 12px;
  align-items: center;
  flex: 0 0 auto;
  margin-left: auto;
}

.epic-filter {
  padding: 6px 10px;
  background: #2d2d2d;
  border: 1px solid #374151;
  border-radius: 4px;
  color: #e5e7eb;
  font-size: 13px;
  width: 120px;
  cursor: pointer;
  height: 32px;
}

.epic-filter:focus {
  outline: none;
  border-color: #4b5563;
}

.search-input {
  padding: 6px 10px;
  background: #2d2d2d;
  border: 1px solid #374151;
  border-radius: 4px;
  color: #e5e7eb;
  font-size: 13px;
  width: 180px;
  height: 32px;
  box-sizing: border-box;
}
.search-input::placeholder { color: #9ca3af; }
.search-input:focus { outline: none; border-color: #4b5563; }

.btn-primary.btn-compact {
  padding: 6px 12px;
  font-size: 13px;
  font-weight: 500;
  border-radius: 4px;
  background: #3b82f6;
  border: none;
  color: #ffffff;
  cursor: pointer;
  transition: all 0.15s ease;
  height: 32px;
  box-sizing: border-box;
}

.btn-primary.btn-compact:hover {
  background: #2563eb;
}


.btn-secondary {
  padding: 8px 16px;
  background: #2d2d2d;
  border: 1px solid #374151;
  border-radius: 6px;
  color: #d1d5db;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.15s ease;
}

.btn-secondary:hover {
  background: #374151;
  border-color: #4b5563;
  color: #ffffff;
}

.btn-secondary.btn-compact {
  padding: 6px 12px;
  font-size: 13px;
  display: flex;
  align-items: center;
  gap: 6px;
}

.btn-secondary.btn-compact:hover {
  background: #374151;
  border-color: #4b5563;
}

.btn-primary {
  padding: 8px 16px;
  background: #3b82f6;
  border: 1px solid #3b82f6;
  border-radius: 6px;
  color: #ffffff;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.15s ease;
}

.btn-primary:hover {
  background: #2563eb;
  border-color: #2563eb;
}

.table-wrapper {
  overflow-x: auto;
  height: calc(100vh - 140px);
  overflow-y: auto;
}

.stories-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
}

.stories-table th.sortable { cursor: pointer; user-select: none; }
.stories-table th .th-content { display: inline-flex; align-items: center; gap: 6px; }
.stories-table th.sortable:hover { color: #cbd5e1; }

.stories-table thead {
  position: sticky;
  top: 0;
  background: #1a1a1a;
  z-index: 10;
}

.stories-table th {
  padding: 8px 16px;
  text-align: left;
  font-weight: 500;
  color: #9ca3af;
  border-bottom: 1px solid #2d2d2d;
  font-size: 11px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  height: 36px;
}

.drag-column { width: 32px; }
.id-column { width: 70px; }
.title-column, .story-title { width: clamp(160px, 20vw, 280px); min-width: 160px; max-width: 280px; }
.epic-column, .story-epic { width: 100px; }
.status-column { width: 110px; }
.priority-column { width: 90px; }
.points-column { width: 60px; }
.implementation-column { width: 120px; }
.version-column { width: 90px; }
.actions-column { width: 40px; }

.story-row {
  border-bottom: 1px solid #2d2d2d;
  transition: all 0.15s ease;
  cursor: pointer;
}

.story-row:hover {
  background: #262626;
}

.story-row:hover .story-title {
  color: #60a5fa;
}

.story-row.selected {
  background: #1e3a8a;
}

.story-row.selected:hover {
  background: #1e40af;
}

.story-row.dragging {
  background: #374151;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.story-row td {
  padding: 8px 16px;
  vertical-align: middle;
}

.story-row .story-id {
  text-align: center;
  vertical-align: top;
  padding-top: 10px;
}

.story-row .story-points-cell {
  text-align: center;
  vertical-align: middle;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 48px; /* match row height */
  padding-top: 0; padding-bottom: 0; /* center within full cell box */
  line-height: 0; /* avoid line box affecting child */
}

.drag-handle {
  cursor: grab;
  color: #6b7280;
  text-align: center;
}

.drag-handle:active {
  cursor: grabbing;
}

.drag-indicator {
  font-size: 12px;
  line-height: 1;
}

.id-badge {
  background: #374151;
  color: #d1d5db;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 10px;
  font-weight: 500;
  font-family: 'Monaco', 'Menlo', monospace;
  display: inline-block;
  vertical-align: baseline;
}

.title-content {
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 0;
}

.title-text {
  color: #ffffff;
  font-weight: 500;
  line-height: 1.2;
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
  min-width: 0;
}

.epic-badge {
  background: #4338ca;
  color: #ffffff;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 9px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  white-space: nowrap;
  flex-shrink: 0;
}

.story-epic {
  padding: 8px 12px;
  text-align: left;
  vertical-align: middle;
}

.status-dropdown, .priority-dropdown {
  display: flex;
  align-items: center;
  gap: 6px;
  position: relative;
  width: 100%;
}

.status-dropdown select, .priority-dropdown select {
  background: transparent;
  border: none;
  color: #d1d5db;
  font-size: 12px;
  cursor: pointer;
  outline: none;
  appearance: none;
  flex: 1;
  min-width: 0;
}

.status-icon, .priority-icon {
  flex-shrink: 0;
}

.status-icon.done { color: #10b981; }
.status-icon.in-progress { color: #3b82f6; }
.status-icon.backlog { color: #6b7280; }

.priority-icon.critical { color: #ef4444; }
.priority-icon.high { color: #f59e0b; }
.priority-icon.medium { color: #6b7280; }
.priority-icon.low { color: #10b981; }

.stories-table td.story-points-cell .points-badge {
  background: #3b82f6;
  color: #ffffff;
  width: 20px !important;
  height: 20px !important;
  border-radius: 50%;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  justify-self: center;
  align-self: center;
  text-align: center;
  font-size: 11px;
  font-weight: 600;
  box-sizing: border-box;
}

.implementation-badge {
  display: flex;
  align-items: center;
  gap: 3px;
  padding: 3px 6px;
  border-radius: 3px;
  font-size: 10px;
  font-weight: 500;
}

.implementation-badge.implemented {
  background: #065f46;
  color: #10b981;
}

.implementation-badge.modified {
  background: #92400e;
  color: #fbbf24;
}

.implementation-badge.not-started {
  background: #374151;
  color: #9ca3af;
}

.version-badge {
  display: flex;
  align-items: center;
  gap: 3px;
  color: #8b5cf6;
  font-size: 10px;
  font-weight: 500;
}

.action-btn {
  background: none;
  border: none;
  color: #6b7280;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.15s ease;
}

.action-btn:hover {
  background: #374151;
  color: #d1d5db;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  text-align: center;
  color: #9ca3af;
  border: none;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.empty-state h3 {
  margin: 0 0 8px 0;
  color: #d1d5db;
  font-size: 18px;
  font-weight: 600;
}

.empty-state p {
  margin: 0 0 24px 0;
  font-size: 14px;
}

/* Scrollbar styling now handled globally in App.css */
