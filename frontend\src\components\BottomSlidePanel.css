/* Bottom Slide Panel Styles */
.bottom-slide-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: flex-end;
  justify-content: center;
  z-index: 1100;
  backdrop-filter: blur(4px);
}

.bottom-slide-panel {
  background: #1a1a1a;
  border-top: 1px solid #374151;
  border-left: 1px solid #374151;
  border-right: 1px solid #374151;
  border-radius: 12px 12px 0 0;
  display: flex;
  flex-direction: column;
  color: #e5e5e5;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  box-shadow: 0 -10px 30px rgba(0, 0, 0, 0.3);
  animation: slideInFromBottom 0.3s ease-out;
  width: 90%;
  max-width: 1200px;
  height: 60vh;
  max-height: 600px;
  min-height: 400px;
  overflow: hidden;
}

@keyframes slideInFromBottom {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

/* Panel Header */
.bottom-slide-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  border-bottom: 1px solid #374151;
  background: #2d2d2d;
  flex-shrink: 0;
  border-radius: 12px 12px 0 0;
}

.bottom-slide-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.bottom-slide-title h2 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #ffffff;
}

.bottom-slide-close-button {
  background: none;
  border: none;
  color: #9ca3af;
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.bottom-slide-close-button:hover {
  background: #374151;
  color: #e5e5e5;
}

/* Panel Content */
.bottom-slide-content {
  flex: 1;
  overflow-y: auto;
  padding: 0;
}

.story-details-content {
  padding: 24px;
  height: 100%;
}

/* Story Header */
.story-header {
  margin-bottom: 24px;
  padding-bottom: 20px;
  border-bottom: 1px solid #374151;
}

.story-badges {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}

.story-title-full {
  margin: 0 0 16px 0;
  color: #ffffff;
  font-size: 20px;
  font-weight: 600;
  line-height: 1.3;
  word-wrap: break-word;
}

.story-meta {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
  align-items: center;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #9ca3af;
  font-size: 14px;
}

/* Story Content */
.story-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
}

.content-section {
  background: #262626;
  border: 1px solid #374151;
  border-radius: 8px;
  padding: 16px;
}

.content-section h4 {
  margin: 0 0 12px 0;
  color: #ffffff;
  font-size: 14px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.content-section p {
  margin: 0;
  color: #d1d5db;
  font-size: 14px;
  line-height: 1.5;
  white-space: pre-wrap;
}

.implementation-status {
  display: flex;
  align-items: center;
}

/* Badges */
.id-badge {
  background: #374151;
  color: #d1d5db;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
  font-family: 'Courier New', monospace;
}

.epic-badge {
  background: #4338ca;
  color: #ffffff;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.priority-badge {
  color: #ffffff;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  text-transform: capitalize;
}

.points-badge {
  background: #3b82f6;
  color: #ffffff;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
}

/* Implementation badges */
.implementation-badge {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  white-space: nowrap;
}

.implementation-badge.implemented {
  background: rgba(16, 185, 129, 0.1);
  color: #10b981;
  border: 1px solid rgba(16, 185, 129, 0.2);
}

.implementation-badge.modified {
  background: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
  border: 1px solid rgba(245, 158, 11, 0.2);
}

.implementation-badge.not-started {
  background: rgba(107, 114, 128, 0.1);
  color: #9ca3af;
  border: 1px solid rgba(107, 114, 128, 0.2);
}

/* Status icons */
.status-icon.done {
  color: #10b981;
}

.status-icon.in-progress {
  color: #3b82f6;
}

.status-icon.ready {
  color: #f59e0b;
}

.status-icon.backlog {
  color: #6b7280;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .bottom-slide-panel {
    width: 95%;
  }
  
  .story-content {
    grid-template-columns: 1fr;
    gap: 16px;
  }
}

@media (max-width: 768px) {
  .bottom-slide-panel {
    width: 100%;
    height: 70vh;
    border-radius: 16px 16px 0 0;
  }
  
  .bottom-slide-header {
    padding: 12px 20px;
    border-radius: 16px 16px 0 0;
  }
  
  .bottom-slide-title h2 {
    font-size: 14px;
  }
  
  .story-details-content {
    padding: 16px;
  }
  
  .story-title-full {
    font-size: 18px;
  }
  
  .story-meta {
    gap: 12px;
  }
  
  .meta-item {
    font-size: 13px;
  }
}

/* Scrollbar styling */
.bottom-slide-content::-webkit-scrollbar {
  width: 8px;
}

.bottom-slide-content::-webkit-scrollbar-track {
  background: transparent;
}

.bottom-slide-content::-webkit-scrollbar-thumb {
  background: #4b5563;
  border-radius: 4px;
}

.bottom-slide-content::-webkit-scrollbar-thumb:hover {
  background: #6b7280;
}
