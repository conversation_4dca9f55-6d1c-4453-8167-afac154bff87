import { useState, useEffect } from 'react';
import { TrendingUp, Clock, Zap, Target, BarChart3, Activity } from 'lucide-react';
import './FlowMetricsDashboard.css';

const FlowMetricsDashboard = ({ projectId }) => {
  const [flowSummary, setFlowSummary] = useState(null);
  const [velocity, setVelocity] = useState(null);
  const [flowMetrics, setFlowMetrics] = useState(null);
  const [flowStatus, setFlowStatus] = useState(null);
  const [workReadiness, setWorkReadiness] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    if (projectId) {
      fetchFlowData();
    }
  }, [projectId]);

  const fetchFlowData = async () => {
    setLoading(true);
    try {
      const [summaryRes, velocityRes, metricsRes, statusRes, readinessRes] = await Promise.all([
        fetch(`http://localhost:3000/projects/${projectId}/flow-summary`),
        fetch(`http://localhost:3000/projects/${projectId}/velocity?sprintCount=5`),
        fetch(`http://localhost:3000/projects/${projectId}/flow-metrics?limit=20`),
        fetch(`http://localhost:3000/projects/${projectId}/flow-status`),
        fetch(`http://localhost:3000/projects/${projectId}/work-readiness`)
      ]);

      if (summaryRes.ok) {
        const summaryData = await summaryRes.json();
        setFlowSummary(summaryData);
      }

      if (velocityRes.ok) {
        const velocityData = await velocityRes.json();
        setVelocity(velocityData);
      }

      if (metricsRes.ok) {
        const metricsData = await metricsRes.json();
        setFlowMetrics(metricsData.metrics || []);
      }

      if (statusRes.ok) {
        const statusData = await statusRes.json();
        setFlowStatus(statusData);
      }

      if (readinessRes.ok) {
        const readinessData = await readinessRes.json();
        setWorkReadiness(readinessData);
      }

    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const formatTime = (hours) => {
    if (hours < 24) {
      return `${Math.round(hours)}h`;
    } else {
      const days = Math.floor(hours / 24);
      const remainingHours = Math.round(hours % 24);
      return remainingHours > 0 ? `${days}d ${remainingHours}h` : `${days}d`;
    }
  };

  const getHealthStatus = (score) => {
    if (score >= 80) return 'good';
    if (score >= 60) return 'warning';
    return 'poor';
  };

  const getHealthStatusText = (score) => {
    if (score >= 80) return 'Optimal Flow';
    if (score >= 60) return 'Needs Attention';
    return 'Flow Issues';
  };

  const getVelocityTrend = () => {
    if (!flowMetrics.length) return 'stable';
    
    const velocityMetrics = flowMetrics
      .filter(m => m.metric_type === 'velocity')
      .slice(0, 3)
      .reverse();
    
    if (velocityMetrics.length < 2) return 'stable';
    
    const latest = velocityMetrics[velocityMetrics.length - 1].metric_value;
    const previous = velocityMetrics[velocityMetrics.length - 2].metric_value;
    
    if (latest > previous * 1.1) return 'up';
    if (latest < previous * 0.9) return 'down';
    return 'stable';
  };

  if (loading) {
    return (
      <div className="flow-metrics-dashboard loading">
        <div className="loading-spinner"></div>
        <p>Loading flow metrics...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flow-metrics-dashboard error">
        <p>Error loading flow metrics: {error}</p>
        <button onClick={fetchFlowData} className="retry-button">
          Retry
        </button>
      </div>
    );
  }

  const velocityTrend = getVelocityTrend();

  return (
    <div className="flow-metrics-dashboard">
      <div className="dashboard-header">
        <h2>Flow Metrics Dashboard</h2>
        <p>Outcome-focused metrics for agentic development</p>
      </div>

      {/* Key Metrics Cards */}
      <div className="metrics-grid">
        <div className="metric-card velocity">
          <div className="metric-icon">
            <TrendingUp className={`icon ${velocityTrend}`} />
          </div>
          <div className="metric-content">
            <div className="metric-value">
              {velocity?.velocity || 0}
            </div>
            <div className="metric-label">
              Velocity (Complexity/Sprint)
            </div>
            <div className="metric-subtitle">
              Based on last {velocity?.sprintCount || 3} sprints
            </div>
          </div>
        </div>

        <div className="metric-card wip">
          <div className="metric-icon">
            <Activity className="icon" />
          </div>
          <div className="metric-content">
            <div className="metric-value">
              {flowSummary?.currentWIP || 0}
            </div>
            <div className="metric-label">
              Current WIP
            </div>
            <div className="metric-subtitle">
              Work in Progress
            </div>
          </div>
        </div>

        <div className="metric-card lead-time">
          <div className="metric-icon">
            <Clock className="icon" />
          </div>
          <div className="metric-content">
            <div className="metric-value">
              {formatTime(flowSummary?.averageLeadTime || 0)}
            </div>
            <div className="metric-label">
              Avg Lead Time
            </div>
            <div className="metric-subtitle">
              Idea to Done
            </div>
          </div>
        </div>

        <div className="metric-card cycle-time">
          <div className="metric-icon">
            <Zap className="icon" />
          </div>
          <div className="metric-content">
            <div className="metric-value">
              {formatTime(flowSummary?.averageCycleTime || 0)}
            </div>
            <div className="metric-label">
              Avg Cycle Time
            </div>
            <div className="metric-subtitle">
              In Progress to Done
            </div>
          </div>
        </div>
      </div>

      {/* WIP Status and Flow Health */}
      {flowStatus && (
        <div className="wip-status">
          <h3>Work In Progress Status</h3>
          <div className="wip-grid">
            <div className="wip-card">
              <div className="wip-header">
                <Activity className="wip-icon" />
                <span>Current WIP</span>
              </div>
              <div className="wip-metrics">
                <div className="wip-metric">
                  <span className="wip-value">{flowStatus.wip?.inProgressCount || 0}</span>
                  <span className="wip-label">Stories</span>
                  <span className="wip-limit">/ {flowStatus.wipLimits?.in_progress || 3}</span>
                </div>
                <div className="wip-metric">
                  <span className="wip-value">{flowStatus.wip?.complexityInProgress || 0}</span>
                  <span className="wip-label">Complexity</span>
                  <span className="wip-limit">/ {flowStatus.wipLimits?.maxComplexityInProgress || 13}</span>
                </div>
              </div>
            </div>

            <div className="wip-card">
              <div className="wip-header">
                <BarChart3 className="wip-icon" />
                <span>Flow Health</span>
              </div>
              <div className="flow-health">
                <div className="health-score">
                  <span className="health-value">{flowStatus.flowHealth || 0}%</span>
                  <span className="health-label">Health Score</span>
                </div>
                <div className="health-indicators">
                  <div className={`health-indicator ${getHealthStatus(flowStatus.flowHealth)}`}>
                    {getHealthStatus(flowStatus.flowHealth) === 'good' ? '✅' :
                     getHealthStatus(flowStatus.flowHealth) === 'warning' ? '⚠️' : '❌'}
                    {getHealthStatusText(flowStatus.flowHealth)}
                  </div>
                </div>
              </div>
            </div>

            {workReadiness && (
              <div className="wip-card">
                <div className="wip-header">
                  <Target className="wip-icon" />
                  <span>Work Readiness</span>
                </div>
                <div className="readiness-status">
                  <div className={`readiness-indicator ${workReadiness.ready ? 'ready' : 'not-ready'}`}>
                    {workReadiness.ready ? '🟢' : '🔴'}
                    <span>{workReadiness.ready ? 'Ready' : 'Not Ready'}</span>
                  </div>
                  <div className="readiness-reason">
                    {workReadiness.reason}
                  </div>
                  {workReadiness.availableStories && (
                    <div className="available-stories">
                      {workReadiness.availableStories} stories available
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Flow Principles */}
      <div className="flow-principles">
        <h3>Flow-Based Development Principles</h3>
        <div className="principles-grid">
          <div className="principle">
            <Target className="principle-icon" />
            <div className="principle-content">
              <h4>Complexity over Time</h4>
              <p>Measure relative difficulty, not hours. AI agents don't fatigue.</p>
            </div>
          </div>
          <div className="principle">
            <TrendingUp className="principle-icon" />
            <div className="principle-content">
              <h4>Velocity Tracking</h4>
              <p>Predictable throughput based on delivered complexity points.</p>
            </div>
          </div>
          <div className="principle">
            <Activity className="principle-icon" />
            <div className="principle-content">
              <h4>WIP Limits</h4>
              <p>Limit concurrent work to optimize flow and reduce context switching.</p>
            </div>
          </div>
          <div className="principle">
            <BarChart3 className="principle-icon" />
            <div className="principle-content">
              <h4>Outcome Focus</h4>
              <p>Measure value delivered per iteration, not time burned.</p>
            </div>
          </div>
        </div>
      </div>

      {/* Recent Metrics History */}
      {flowMetrics.length > 0 && (
        <div className="metrics-history">
          <h3>Recent Flow Metrics</h3>
          <div className="metrics-table">
            <div className="table-header">
              <div>Date</div>
              <div>Type</div>
              <div>Value</div>
              <div>Complexity</div>
              <div>Stories</div>
            </div>
            {flowMetrics.slice(0, 10).map((metric, index) => (
              <div key={index} className="table-row">
                <div>{new Date(metric.measurement_date).toLocaleDateString()}</div>
                <div className={`metric-type ${metric.metric_type}`}>
                  {metric.metric_type.replace('_', ' ')}
                </div>
                <div>
                  {metric.metric_type.includes('time') 
                    ? formatTime(metric.metric_value)
                    : Math.round(metric.metric_value * 100) / 100
                  }
                </div>
                <div>{metric.complexity_delivered || '-'}</div>
                <div>{metric.stories_delivered || '-'}</div>
              </div>
            ))}
          </div>
        </div>
      )}

      {flowMetrics.length === 0 && (
        <div className="no-metrics">
          <BarChart3 size={48} />
          <h3>No Flow Metrics Yet</h3>
          <p>Complete some user stories to start seeing flow metrics data.</p>
        </div>
      )}
    </div>
  );
};

export default FlowMetricsDashboard;
