.flow-metrics-dashboard {
  padding: 24px;
  background: #1a1a1a;
  color: #e5e5e5;
  min-height: 100vh;
}

.dashboard-header {
  margin-bottom: 32px;
}

.dashboard-header h2 {
  font-size: 28px;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: #ffffff;
}

.dashboard-header p {
  font-size: 16px;
  color: #9ca3af;
  margin: 0;
}

/* Metrics Grid */
.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
  margin-bottom: 40px;
}

.metric-card {
  background: #262626;
  border: 1px solid #404040;
  border-radius: 12px;
  padding: 24px;
  display: flex;
  align-items: center;
  gap: 16px;
  transition: all 0.2s ease;
}

.metric-card:hover {
  border-color: #525252;
  transform: translateY(-2px);
}

.metric-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.metric-card.velocity .metric-icon {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.metric-card.wip .metric-icon {
  background: linear-gradient(135deg, #f59e0b, #d97706);
}

.metric-card.lead-time .metric-icon {
  background: linear-gradient(135deg, #10b981, #059669);
}

.metric-card.cycle-time .metric-icon {
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
}

.metric-icon .icon {
  width: 24px;
  height: 24px;
  color: white;
}

.metric-icon .icon.up {
  color: #10b981;
}

.metric-icon .icon.down {
  color: #ef4444;
}

.metric-icon .icon.stable {
  color: #6b7280;
}

.metric-content {
  flex: 1;
}

.metric-value {
  font-size: 18px;
  font-weight: 700;
  color: #ffffff;
  line-height: 1;
  margin-bottom: 4px;
}

.metric-label {
  font-size: 16px;
  font-weight: 500;
  color: #e5e5e5;
  margin-bottom: 2px;
}

.metric-subtitle {
  font-size: 14px;
  color: #9ca3af;
}

/* WIP Status */
.wip-status {
  margin-bottom: 40px;
}

.wip-status h3 {
  font-size: 20px;
  font-weight: 600;
  color: #ffffff;
  margin: 0 0 20px 0;
}

.wip-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

.wip-card {
  background: #262626;
  border: 1px solid #404040;
  border-radius: 8px;
  padding: 20px;
}

.wip-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
  font-weight: 600;
  color: #e5e5e5;
}

.wip-icon {
  width: 18px;
  height: 18px;
  color: #60a5fa;
}

.wip-metrics {
  display: flex;
  gap: 20px;
}

.wip-metric {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.wip-value {
  font-size: 24px;
  font-weight: 700;
  color: #ffffff;
}

.wip-label {
  font-size: 12px;
  color: #9ca3af;
  margin-top: 4px;
}

.wip-limit {
  font-size: 14px;
  color: #6b7280;
  margin-top: 2px;
}

.flow-health {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.health-score {
  text-align: center;
}

.health-value {
  font-size: 32px;
  font-weight: 700;
  color: #ffffff;
  display: block;
}

.health-label {
  font-size: 14px;
  color: #9ca3af;
}

.health-indicators {
  display: flex;
  justify-content: center;
}

.health-indicator {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
}

.health-indicator.good {
  background: rgba(16, 185, 129, 0.2);
  color: #6ee7b7;
}

.health-indicator.warning {
  background: rgba(245, 158, 11, 0.2);
  color: #fbbf24;
}

.health-indicator.poor {
  background: rgba(239, 68, 68, 0.2);
  color: #fca5a5;
}

.readiness-status {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.readiness-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
}

.readiness-indicator.ready {
  color: #10b981;
}

.readiness-indicator.not-ready {
  color: #ef4444;
}

.readiness-reason {
  font-size: 14px;
  color: #d1d5db;
  line-height: 1.4;
}

.available-stories {
  font-size: 12px;
  color: #9ca3af;
}

/* Flow Principles */
.flow-principles {
  margin-bottom: 40px;
}

.flow-principles h3 {
  font-size: 20px;
  font-weight: 600;
  color: #ffffff;
  margin: 0 0 20px 0;
}

.principles-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.principle {
  background: #262626;
  border: 1px solid #404040;
  border-radius: 8px;
  padding: 20px;
  display: flex;
  align-items: flex-start;
  gap: 16px;
}

.principle-icon {
  width: 20px;
  height: 20px;
  color: #60a5fa;
  flex-shrink: 0;
  margin-top: 2px;
}

.principle-content h4 {
  font-size: 16px;
  font-weight: 600;
  color: #ffffff;
  margin: 0 0 8px 0;
}

.principle-content p {
  font-size: 14px;
  color: #9ca3af;
  margin: 0;
  line-height: 1.5;
}

/* Metrics History */
.metrics-history {
  margin-bottom: 40px;
}

.metrics-history h3 {
  font-size: 20px;
  font-weight: 600;
  color: #ffffff;
  margin: 0 0 20px 0;
}

.metrics-table {
  background: #262626;
  border: 1px solid #404040;
  border-radius: 8px;
  overflow: hidden;
}

.table-header {
  /* display: grid;
  grid-template-columns: 120px 120px 100px 100px 80px; */
  gap: 16px;
  padding: 16px 20px;
  background: #1f1f1f;
  border-bottom: 1px solid #404040;
  font-weight: 600;
  color: #e5e5e5;
  font-size: 14px;
}

.table-row {
  display: grid;
  grid-template-columns: 120px 120px 100px 100px 80px;
  gap: 16px;
  padding: 12px 20px;
  border-bottom: 1px solid #333333;
  font-size: 14px;
  color: #d1d5db;
}

.table-row:last-child {
  border-bottom: none;
}

.table-row:hover {
  background: #2d2d2d;
}

.metric-type {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  text-transform: capitalize;
  width: fit-content;
}

.metric-type.velocity {
  background: rgba(59, 130, 246, 0.2);
  color: #93c5fd;
}

.metric-type.lead_time {
  background: rgba(16, 185, 129, 0.2);
  color: #6ee7b7;
}

.metric-type.cycle_time {
  background: rgba(139, 92, 246, 0.2);
  color: #c4b5fd;
}

.metric-type.throughput {
  background: rgba(245, 158, 11, 0.2);
  color: #fbbf24;
}

/* Loading and Error States */
.flow-metrics-dashboard.loading,
.flow-metrics-dashboard.error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  text-align: center;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #404040;
  border-top: 3px solid #60a5fa;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.retry-button {
  background: #3b82f6;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  margin-top: 16px;
}

.retry-button:hover {
  background: #2563eb;
}

/* No Metrics State */
.no-metrics {
  text-align: center;
  padding: 60px 20px;
  color: #9ca3af;
}

.no-metrics svg {
  margin-bottom: 16px;
  opacity: 0.5;
}

.no-metrics h3 {
  font-size: 18px;
  color: #d1d5db;
  margin: 0 0 8px 0;
}

.no-metrics p {
  font-size: 14px;
  margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .flow-metrics-dashboard {
    padding: 16px;
  }
  
  .metrics-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .principles-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .table-header,
  .table-row {
    grid-template-columns: 1fr;
    gap: 8px;
  }
  
  .table-header > div,
  .table-row > div {
    padding: 4px 0;
  }
}
