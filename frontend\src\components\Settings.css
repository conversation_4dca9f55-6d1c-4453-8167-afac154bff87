/* Settings Component - Consistent with site design */
.settings {
  flex: 1;
  background: #1a1a1a;
  color: #e5e5e5;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  overflow-y: auto;
}

.settings.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100vh;
}

.loading-spinner {
  text-align: center;
  color: #9ca3af;
}

.loading-spinner .animate-spin {
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Header */
.settings-header {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 24px 32px;
  border-bottom: 1px solid #2d2d2d;
  background: #1a1a1a;
}

.settings-header h1 {
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 0;
  color: #ffffff;
  font-size: 28px;
  font-weight: 700;
}

.settings-header p {
  margin: 0;
  color: #9ca3af;
  font-size: 16px;
}

/* Tab Navigation */
.settings-tabs {
  display: flex;
  background: #1a1a1a;
  border-bottom: 1px solid #2d2d2d;
  padding: 0 32px;
}

.tab-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 16px 24px;
  background: none;
  border: none;
  color: #9ca3af;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.15s ease;
  border-bottom: 2px solid transparent;
}

.tab-button:hover {
  color: #e5e5e5;
  background: #2d2d2d;
}

.tab-button.active {
  color: #3b82f6;
  border-bottom-color: #3b82f6;
}

/* Content */
.settings-content {
  padding: 32px;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

/* Settings Sections */
.settings-section {
  background: #1a1a1a;
  border: 1px solid #2d2d2d;
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 32px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.section-header h2 {
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 0;
  color: #ffffff;
  font-size: 20px;
  font-weight: 600;
}

/* Buttons */
.btn-primary,
.btn-secondary,
.btn-danger,
.btn-success {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px 20px;
  border: 1px solid transparent;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.15s ease;
  text-align: center;
}

.btn-primary {
  background: #3b82f6;
  color: #ffffff;
  border-color: #3b82f6;
}

.btn-primary:hover:not(:disabled) {
  background: #2563eb;
  border-color: #2563eb;
}

.btn-secondary {
  background: #2d2d2d;
  color: #e5e5e5;
  border-color: #374151;
}

.btn-secondary:hover:not(:disabled) {
  background: #374151;
  border-color: #4b5563;
}

.btn-danger {
  background: #dc2626;
  color: #ffffff;
  border-color: #dc2626;
}

.btn-danger:hover:not(:disabled) {
  background: #b91c1c;
  border-color: #b91c1c;
}

.btn-success {
  background: #059669;
  color: #ffffff;
  border-color: #059669;
}

.btn-success:hover:not(:disabled) {
  background: #047857;
  border-color: #047857;
}

.btn-primary:disabled,
.btn-secondary:disabled,
.btn-danger:disabled,
.btn-success:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Stats Grid */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

.stat-card {
  background: #0f0f0f;
  border-radius: 8px;
  padding: 20px;
  border: 1px solid #2d2d2d;
  display: flex;
  align-items: center;
  gap: 16px;
  transition: border-color 0.15s ease;
}

.stat-card:hover {
  border-color: #374151;
}

.stat-icon {
  background: #3b82f6;
  border-radius: 8px;
  padding: 12px;
  color: white;
  flex-shrink: 0;
}

.stat-content h3 {
  margin: 0 0 4px 0;
  font-size: 24px;
  color: #ffffff;
  font-weight: 600;
}

.stat-content p {
  margin: 0;
  color: #9ca3af;
  font-size: 14px;
  font-weight: 500;
}

/* Configuration Grid */
.config-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.config-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.config-item label {
  color: #d1d5db;
  font-weight: 500;
  font-size: 14px;
}

.config-item select,
.config-item input {
  background: #0f0f0f;
  border: 1px solid #2d2d2d;
  border-radius: 6px;
  padding: 10px 12px;
  color: #e5e5e5;
  font-size: 14px;
  transition: border-color 0.15s ease;
}

.config-item select:focus,
.config-item input:focus {
  outline: none;
  border-color: #3b82f6;
}

.config-item select:hover,
.config-item input:hover {
  border-color: #374151;
}

/* Configuration Checkboxes */
.config-checkboxes {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-top: 24px;
  padding-top: 24px;
  border-top: 1px solid #2d2d2d;
}

.checkbox-item {
  display: flex;
  align-items: center;
}

.checkbox-item label {
  display: flex;
  align-items: center;
  gap: 12px;
  color: #d1d5db;
  font-weight: 500;
  font-size: 14px;
  cursor: pointer;
}

.checkbox-item input[type="checkbox"] {
  margin: 0;
  transform: scale(1.1);
  accent-color: #3b82f6;
}

.checkbox-item input[type="checkbox"]:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.checkbox-item label:has(input:disabled) {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Models Summary */
.models-summary {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
}

.model-summary-card {
  background: #0f0f0f;
  border: 1px solid #2d2d2d;
  border-radius: 6px;
  padding: 16px;
  transition: border-color 0.15s ease;
}

.model-summary-card:hover {
  border-color: #374151;
}

.model-summary-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.model-summary-header h4 {
  margin: 0;
  color: #ffffff;
  font-size: 16px;
  font-weight: 600;
}

.tier-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
  color: white;
  text-transform: uppercase;
}

.model-summary-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.model-provider {
  color: #9ca3af;
  font-size: 14px;
}

.model-cost {
  color: #d1d5db;
  font-size: 13px;
  font-family: monospace;
}



/* Responsive Design */
@media (max-width: 768px) {
  .settings-header {
    padding: 20px;
  }

  .settings-tabs {
    padding: 0 20px;
  }

  .tab-button {
    padding: 12px 16px;
  }

  .settings-content {
    padding: 20px;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .config-grid {
    grid-template-columns: 1fr;
  }

  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .scheduler-controls {
    flex-direction: column;
  }

  .settings-header h1 {
    font-size: 24px;
  }
}
