/**
 * Flow Metrics Service
 * Automatically calculates and updates flow metrics when stories change status
 * Integrates with AI agents and sprint management
 */

const { 
  updateStoryFlowMetrics, 
  updateSprintFlowMetrics, 
  calculateProjectVelocity,
  recordFlowMetric,
  getStoryById,
  getSprintById 
} = require('./flow-metrics');

class FlowMetricsService {
  constructor() {
    this.isInitialized = false;
  }

  /**
   * Initialize the flow metrics service
   */
  async initialize() {
    if (this.isInitialized) return;
    
    console.log('🔄 Initializing Flow Metrics Service...');
    
    // Migrate existing stories to have complexity scores if they don't
    await this.migrateExistingStories();
    
    // Calculate initial sprint metrics for active sprints
    await this.calculateInitialSprintMetrics();
    
    this.isInitialized = true;
    console.log('✅ Flow Metrics Service initialized');
  }

  /**
   * Migrate existing stories to have complexity scores based on story points
   */
  async migrateExistingStories() {
    const db = require('./database');
    
    try {
      // Get stories without complexity scores
      const stories = await new Promise((resolve, reject) => {
        db.all(
          `SELECT id, story_points FROM user_stories WHERE complexity_score IS NULL OR complexity_score = 0`,
          [],
          (err, rows) => {
            if (err) reject(err);
            else resolve(rows || []);
          }
        );
      });

      console.log(`🔄 Migrating ${stories.length} stories to complexity scoring...`);

      for (const story of stories) {
        // Map story points to complexity scores
        let complexityScore = 3; // Default
        
        if (story.story_points <= 1) complexityScore = 1; // XS
        else if (story.story_points <= 2) complexityScore = 2; // S
        else if (story.story_points <= 3) complexityScore = 3; // M
        else if (story.story_points <= 5) complexityScore = 5; // L
        else if (story.story_points <= 8) complexityScore = 8; // XL
        else complexityScore = 13; // XXL

        await new Promise((resolve, reject) => {
          db.run(
            `UPDATE user_stories SET complexity_score = ? WHERE id = ?`,
            [complexityScore, story.id],
            function(err) {
              if (err) reject(err);
              else resolve();
            }
          );
        });
      }

      console.log(`✅ Migrated ${stories.length} stories to complexity scoring`);
    } catch (error) {
      console.error('❌ Error migrating stories:', error);
    }
  }

  /**
   * Calculate initial metrics for active sprints
   */
  async calculateInitialSprintMetrics() {
    const db = require('./database');
    
    try {
      // Get active sprints
      const sprints = await new Promise((resolve, reject) => {
        db.all(
          `SELECT id FROM sprints WHERE status IN ('planning', 'active')`,
          [],
          (err, rows) => {
            if (err) reject(err);
            else resolve(rows || []);
          }
        );
      });

      console.log(`🔄 Calculating initial metrics for ${sprints.length} active sprints...`);

      for (const sprint of sprints) {
        await updateSprintFlowMetrics(sprint.id);
      }

      console.log(`✅ Calculated initial metrics for ${sprints.length} sprints`);
    } catch (error) {
      console.error('❌ Error calculating initial sprint metrics:', error);
    }
  }

  /**
   * Handle story status change and update flow metrics
   */
  async onStoryStatusChange(storyId, newStatus, oldStatus, projectId) {
    try {
      console.log(`📊 Flow metrics update: Story ${storyId} changed from ${oldStatus} to ${newStatus}`);
      
      // Update story flow metrics
      await updateStoryFlowMetrics(storyId, newStatus, oldStatus);
      
      // Get story to check if it's in a sprint
      const story = await getStoryById(storyId);
      if (story && story.sprint_id) {
        // Update sprint metrics
        await updateSprintFlowMetrics(story.sprint_id);
        
        // Record velocity metric if story completed
        if (newStatus === 'done' && oldStatus !== 'done') {
          const velocity = await calculateProjectVelocity(projectId, 1);
          await recordFlowMetric(
            projectId, 
            story.sprint_id, 
            'velocity', 
            velocity,
            {
              complexity_delivered: story.complexity_score || 3,
              stories_delivered: 1,
              metadata: { story_id: storyId, story_title: story.title }
            }
          );
        }
      }
      
      console.log(`✅ Flow metrics updated for story ${storyId}`);
    } catch (error) {
      console.error(`❌ Error updating flow metrics for story ${storyId}:`, error);
    }
  }

  /**
   * Handle sprint completion and record final metrics
   */
  async onSprintComplete(sprintId, projectId) {
    try {
      console.log(`📊 Recording final metrics for completed sprint ${sprintId}`);
      
      const sprint = await getSprintById(sprintId);
      if (!sprint) return;

      // Record final sprint metrics
      await recordFlowMetric(
        projectId,
        sprintId,
        'throughput',
        sprint.throughput_per_day || 0,
        {
          complexity_delivered: sprint.complexity_completed || 0,
          stories_delivered: sprint.stories_completed || 0,
          period_start: sprint.start_date,
          period_end: sprint.end_date,
          metadata: { 
            sprint_name: sprint.name,
            total_stories: sprint.stories_total,
            total_complexity: sprint.complexity_total
          }
        }
      );

      await recordFlowMetric(
        projectId,
        sprintId,
        'lead_time',
        sprint.average_lead_time || 0,
        {
          complexity_delivered: sprint.complexity_completed || 0,
          stories_delivered: sprint.stories_completed || 0,
          period_start: sprint.start_date,
          period_end: sprint.end_date
        }
      );

      await recordFlowMetric(
        projectId,
        sprintId,
        'cycle_time',
        sprint.average_cycle_time || 0,
        {
          complexity_delivered: sprint.complexity_completed || 0,
          stories_delivered: sprint.stories_completed || 0,
          period_start: sprint.start_date,
          period_end: sprint.end_date
        }
      );

      console.log(`✅ Final metrics recorded for sprint ${sprintId}`);
    } catch (error) {
      console.error(`❌ Error recording final sprint metrics:`, error);
    }
  }

  /**
   * Calculate and return current WIP (Work in Progress) for a project
   */
  async getCurrentWIP(projectId) {
    const db = require('./database');
    
    try {
      const wipCount = await new Promise((resolve, reject) => {
        db.get(
          `SELECT COUNT(*) as count FROM user_stories 
           WHERE project_id = ? AND status = 'in_progress'`,
          [projectId],
          (err, row) => {
            if (err) reject(err);
            else resolve(row?.count || 0);
          }
        );
      });

      return wipCount;
    } catch (error) {
      console.error('Error calculating WIP:', error);
      return 0;
    }
  }

  /**
   * Check if adding a story to WIP would exceed limits
   */
  async canAddToWIP(projectId, sprintId) {
    try {
      const currentWIP = await this.getCurrentWIP(projectId);
      
      // Get sprint WIP limit
      const sprint = await getSprintById(sprintId);
      const wipLimit = sprint?.wip_limit || 5;
      
      return currentWIP < wipLimit;
    } catch (error) {
      console.error('Error checking WIP limits:', error);
      return true; // Default to allowing if check fails
    }
  }

  /**
   * Get flow metrics summary for a project
   */
  async getProjectFlowSummary(projectId) {
    const db = require('./database');
    
    try {
      // Get recent velocity
      const velocity = await calculateProjectVelocity(projectId, 3);
      
      // Get current WIP
      const currentWIP = await this.getCurrentWIP(projectId);
      
      // Get average lead and cycle times from recent sprints
      const recentMetrics = await new Promise((resolve, reject) => {
        db.all(
          `SELECT metric_type, AVG(metric_value) as avg_value 
           FROM flow_metrics 
           WHERE project_id = ? AND metric_type IN ('lead_time', 'cycle_time')
           AND measurement_date >= date('now', '-30 days')
           GROUP BY metric_type`,
          [projectId],
          (err, rows) => {
            if (err) reject(err);
            else resolve(rows || []);
          }
        );
      });

      const summary = {
        velocity,
        currentWIP,
        averageLeadTime: 0,
        averageCycleTime: 0
      };

      recentMetrics.forEach(metric => {
        if (metric.metric_type === 'lead_time') {
          summary.averageLeadTime = Math.round(metric.avg_value || 0);
        } else if (metric.metric_type === 'cycle_time') {
          summary.averageCycleTime = Math.round(metric.avg_value || 0);
        }
      });

      return summary;
    } catch (error) {
      console.error('Error getting project flow summary:', error);
      return {
        velocity: 0,
        currentWIP: 0,
        averageLeadTime: 0,
        averageCycleTime: 0
      };
    }
  }
}

// Export singleton instance
const flowMetricsService = new FlowMetricsService();
module.exports = flowMetricsService;
