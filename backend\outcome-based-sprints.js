/**
 * Outcome-Based Sprint Management
 * Transforms sprints from fixed time periods to outcome-based milestones
 * Focuses on value delivery rather than calendar adherence
 */

const db = require('./database');
const flowMetricsService = require('./flow-metrics-service');

class OutcomeBasedSprintManager {
  constructor() {
    this.completionCriteria = {
      // Minimum percentage of complexity that must be delivered
      minComplexityDelivered: 0.8, // 80%
      
      // Minimum percentage of stories that must be completed
      minStoriesCompleted: 0.75, // 75%
      
      // Maximum days a sprint can run (safety net)
      maxSprintDays: 21, // 3 weeks
      
      // Minimum days before sprint can be completed
      minSprintDays: 3
    };
  }

  /**
   * Check if a sprint can be completed based on outcome criteria
   */
  async canCompleteSprint(sprintId) {
    try {
      const sprint = await this.getSprintWithMetrics(sprintId);
      if (!sprint) return { canComplete: false, reason: 'Sprint not found' };

      const daysSinceStart = this.getDaysSinceStart(sprint.start_date);
      
      // Safety checks
      if (daysSinceStart < this.completionCriteria.minSprintDays) {
        return { 
          canComplete: false, 
          reason: `Sprint must run for at least ${this.completionCriteria.minSprintDays} days` 
        };
      }

      if (daysSinceStart > this.completionCriteria.maxSprintDays) {
        return { 
          canComplete: true, 
          reason: 'Maximum sprint duration reached - auto-completion triggered',
          autoComplete: true
        };
      }

      // Outcome-based completion criteria
      const complexityRatio = sprint.complexity_total > 0 
        ? sprint.complexity_completed / sprint.complexity_total 
        : 0;
      
      const storiesRatio = sprint.stories_total > 0 
        ? sprint.stories_completed / sprint.stories_total 
        : 0;

      const complexityMet = complexityRatio >= this.completionCriteria.minComplexityDelivered;
      const storiesMet = storiesRatio >= this.completionCriteria.minStoriesCompleted;

      if (complexityMet && storiesMet) {
        return {
          canComplete: true,
          reason: 'Outcome criteria met - sprint goals achieved',
          metrics: {
            complexityDelivered: Math.round(complexityRatio * 100),
            storiesCompleted: Math.round(storiesRatio * 100),
            daysTaken: daysSinceStart
          }
        };
      }

      // Provide feedback on what's missing
      const missing = [];
      if (!complexityMet) {
        missing.push(`Complexity: ${Math.round(complexityRatio * 100)}% (need ${Math.round(this.completionCriteria.minComplexityDelivered * 100)}%)`);
      }
      if (!storiesMet) {
        missing.push(`Stories: ${Math.round(storiesRatio * 100)}% (need ${Math.round(this.completionCriteria.minStoriesCompleted * 100)}%)`);
      }

      return {
        canComplete: false,
        reason: `Outcome criteria not met: ${missing.join(', ')}`,
        metrics: {
          complexityDelivered: Math.round(complexityRatio * 100),
          storiesCompleted: Math.round(storiesRatio * 100),
          daysTaken: daysSinceStart
        }
      };

    } catch (error) {
      console.error('Error checking sprint completion criteria:', error);
      return { canComplete: false, reason: 'Error checking completion criteria' };
    }
  }

  /**
   * Complete a sprint based on outcome criteria
   */
  async completeSprint(sprintId, projectId, reason = 'Outcome criteria met') {
    try {
      // Update sprint status
      await new Promise((resolve, reject) => {
        db.run(
          `UPDATE sprints SET 
           status = 'completed',
           end_date = date('now'),
           updated_at = CURRENT_TIMESTAMP
           WHERE id = ? AND project_id = ?`,
          [sprintId, projectId],
          function(err) {
            if (err) reject(err);
            else resolve();
          }
        );
      });

      // Record final flow metrics
      await flowMetricsService.onSprintComplete(sprintId, projectId);

      console.log(`✅ Sprint ${sprintId} completed: ${reason}`);
      return { success: true, reason };

    } catch (error) {
      console.error('Error completing sprint:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Suggest sprint scope based on team velocity
   */
  async suggestSprintScope(projectId, targetDays = 14) {
    try {
      // Get recent velocity (complexity per day)
      const velocity = await this.calculateDailyVelocity(projectId);
      
      if (velocity === 0) {
        // No historical data - use conservative estimate
        return {
          suggestedComplexity: 21, // Default complexity capacity
          suggestedStories: 7,
          confidence: 'low',
          reason: 'No historical velocity data - using conservative estimate'
        };
      }

      const suggestedComplexity = Math.round(velocity * targetDays);
      const avgComplexityPerStory = await this.getAverageComplexityPerStory(projectId);
      const suggestedStories = Math.round(suggestedComplexity / avgComplexityPerStory);

      return {
        suggestedComplexity,
        suggestedStories,
        dailyVelocity: velocity,
        confidence: 'medium',
        reason: `Based on recent velocity of ${velocity.toFixed(1)} complexity points per day`
      };

    } catch (error) {
      console.error('Error suggesting sprint scope:', error);
      return {
        suggestedComplexity: 21,
        suggestedStories: 7,
        confidence: 'low',
        reason: 'Error calculating velocity - using default estimate'
      };
    }
  }

  /**
   * Check all active sprints for auto-completion
   */
  async checkActiveSprintsForCompletion() {
    try {
      const activeSprints = await new Promise((resolve, reject) => {
        db.all(
          `SELECT id, project_id FROM sprints WHERE status = 'active'`,
          [],
          (err, rows) => {
            if (err) reject(err);
            else resolve(rows || []);
          }
        );
      });

      const results = [];
      for (const sprint of activeSprints) {
        const completionCheck = await this.canCompleteSprint(sprint.id);
        
        if (completionCheck.canComplete && completionCheck.autoComplete) {
          const result = await this.completeSprint(
            sprint.id, 
            sprint.project_id, 
            completionCheck.reason
          );
          results.push({ sprintId: sprint.id, ...result });
        }
      }

      return results;
    } catch (error) {
      console.error('Error checking active sprints:', error);
      return [];
    }
  }

  // Helper methods

  async getSprintWithMetrics(sprintId) {
    return new Promise((resolve, reject) => {
      db.get(
        `SELECT * FROM sprints WHERE id = ?`,
        [sprintId],
        (err, row) => {
          if (err) reject(err);
          else resolve(row);
        }
      );
    });
  }

  getDaysSinceStart(startDate) {
    const start = new Date(startDate);
    const now = new Date();
    const diffTime = Math.abs(now - start);
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }

  async calculateDailyVelocity(projectId) {
    try {
      const completedSprints = await new Promise((resolve, reject) => {
        db.all(
          `SELECT complexity_completed, start_date, end_date 
           FROM sprints 
           WHERE project_id = ? AND status = 'completed'
           AND complexity_completed > 0
           ORDER BY end_date DESC 
           LIMIT 3`,
          [projectId],
          (err, rows) => {
            if (err) reject(err);
            else resolve(rows || []);
          }
        );
      });

      if (completedSprints.length === 0) return 0;

      let totalComplexity = 0;
      let totalDays = 0;

      for (const sprint of completedSprints) {
        const start = new Date(sprint.start_date);
        const end = new Date(sprint.end_date);
        const days = Math.ceil((end - start) / (1000 * 60 * 60 * 24));
        
        totalComplexity += sprint.complexity_completed;
        totalDays += days;
      }

      return totalDays > 0 ? totalComplexity / totalDays : 0;
    } catch (error) {
      console.error('Error calculating daily velocity:', error);
      return 0;
    }
  }

  async getAverageComplexityPerStory(projectId) {
    try {
      const result = await new Promise((resolve, reject) => {
        db.get(
          `SELECT AVG(complexity_score) as avg_complexity 
           FROM user_stories 
           WHERE project_id = ? AND status = 'done'`,
          [projectId],
          (err, row) => {
            if (err) reject(err);
            else resolve(row);
          }
        );
      });

      return result?.avg_complexity || 3; // Default to medium complexity
    } catch (error) {
      console.error('Error calculating average complexity per story:', error);
      return 3;
    }
  }
}

module.exports = new OutcomeBasedSprintManager();
