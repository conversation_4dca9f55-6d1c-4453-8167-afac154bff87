import { useState } from 'react';
import { FileText, User, Target, GitBranch, Clock, AlertTriangle } from 'lucide-react';
import CommentTimeline from './CommentTimeline';
import TasksList from './TasksList';
import SlideoutPanel from './SlideoutPanel';
import './UserStoryModal.css';

const StoryDetailsModal = ({ isOpen, onClose, story, projectId }) => {
  const [commentText, setCommentText] = useState('');
  const [posting, setPosting] = useState(false);
  const [submitError, setSubmitError] = useState(null);
  const [refreshKey, setRefreshKey] = useState(0);

  const handleAddComment = async () => {
    const text = commentText.trim();
    if (!text) return;
    try {
      setPosting(true);
      setSubmitError(null);
      const res = await fetch(`/api/projects/${projectId}/comments`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          entity_type: 'user_story',
          entity_id: story.id,
          author_type: 'human',
          author_name: 'User',
          comment_text: text,
          comment_type: 'update',
          is_internal: false,
          priority: 'normal'
        }),
      });
      if (!res.ok) {
        const data = await res.json().catch(() => ({}));
        throw new Error(data.error || 'Failed to add comment');
      }
      setCommentText('');
      setRefreshKey((k) => k + 1);
    } catch (err) {
      setSubmitError(err.message);
    } finally {
      setPosting(false);
    }
  };

  if (!isOpen || !story) return null;

  return (
    <SlideoutPanel
      isOpen={isOpen}
      onClose={onClose}
      title="Story Details"
      icon={FileText}
      width="800px"
      className="large"
    >
      <div className="slideout-content padded">
        <div className="story-modal-content">
          <div style={{ marginBottom: 16 }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: 8, marginBottom: 8 }}>
              <span className="id-badge">{story.id?.slice(-6)}</span>
              {story.epic && <span className="epic-badge">{story.epic}</span>}
            </div>
            <h3 style={{ margin: '0 0 8px 0', color: '#fff' }}>{story.title}</h3>
            <div style={{ display: 'flex', gap: 12, flexWrap: 'wrap', color: '#9ca3af', fontSize: 13 }}>
              <div style={{ display: 'inline-flex', alignItems: 'center', gap: 6 }}>
                <Clock size={14} /> Status: <strong style={{ color: '#d1d5db' }}>{story.status}</strong>
              </div>
              <div style={{ display: 'inline-flex', alignItems: 'center', gap: 6 }}>
                <AlertTriangle size={14} /> Priority: <strong style={{ color: '#d1d5db' }}>{story.priority}</strong>
              </div>
              <div style={{ display: 'inline-flex', alignItems: 'center', gap: 6 }}>
                <Target size={14} /> Points: <strong style={{ color: '#d1d5db' }}>{story.story_points}</strong>
              </div>
              {story.implemented_in_version && (
                <div style={{ display: 'inline-flex', alignItems: 'center', gap: 6 }}>
                  <GitBranch size={14} /> Version: <strong style={{ color: '#d1d5db' }}>v{story.implemented_in_version}</strong>
                </div>
              )}
            </div>
          </div>

          <div style={{ display: 'grid', gridTemplateColumns: '1fr', gap: 16 }}>
            <div className="story-form-group">
              <label>Description</label>
              <div style={{ whiteSpace: 'pre-wrap', color: '#d1d5db' }}>
                {story.description || '—'}
              </div>
            </div>

            <div className="story-form-group">
              <label>Acceptance Criteria</label>
              <div style={{ whiteSpace: 'pre-wrap', color: '#d1d5db' }}>
                {story.acceptance_criteria || '—'}
              </div>
            </div>

            {story.business_value && (
              <div className="story-form-group">
                <label>Business Value</label>
                <div style={{ whiteSpace: 'pre-wrap', color: '#d1d5db' }}>
                  {story.business_value}
                </div>
              </div>
            )}
          </div>

          {/* AI Generated Tasks Section */}
          <TasksList projectId={projectId} storyId={story.id} />

          <div style={{ marginTop: 20 }}>
            <CommentTimeline
              key={refreshKey}
              projectId={projectId}
              entityType="user_story"
              entityId={story.id}
              showInternalComments={false}
              maxHeight={'320px'}
            />
          </div>

          <div className="story-form-group" style={{ marginTop: 12 }}>
            <label>Add a comment</label>
            <textarea
              value={commentText}
              onChange={(e) => setCommentText(e.target.value)}
              rows={3}
              placeholder="Write a comment..."
            />
            {submitError && <span className="error-message">{submitError}</span>}
            <div className="story-modal-actions" style={{ justifyContent: 'flex-end', paddingTop: 8 }}>
              <button
                type="button"
                className="btn-primary"
                onClick={handleAddComment}
                disabled={posting || !commentText.trim()}
              >
                {posting ? 'Posting...' : 'Add Comment'}
              </button>
            </div>
          </div>


          <div className="story-modal-actions">
            <button type="button" className="btn-secondary" onClick={onClose}>
              Close
            </button>
          </div>
        </div>
      </div>
    </SlideoutPanel>
  );
};

export default StoryDetailsModal;

