/* Modal Backdrop */
.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
}

/* Modal Container */
.confirmation-modal {
  background: #1f2937;
  border: 1px solid #374151;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  max-width: 480px;
  width: 90%;
  max-height: 90vh;
  overflow: hidden;
  animation: modalSlideIn 0.2s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* Modal Header */
.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24px 24px 16px 24px;
  border-bottom: 1px solid #374151;
}

.modal-title-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.modal-title {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #f9fafb;
}

.modal-icon {
  flex-shrink: 0;
}

.modal-icon.danger {
  color: #ef4444;
}

.modal-icon.warning {
  color: #f59e0b;
}

.modal-close-button {
  background: none;
  border: none;
  color: #9ca3af;
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.modal-close-button:hover {
  background: #374151;
  color: #f9fafb;
}

/* Modal Body */
.modal-body {
  padding: 16px 24px 24px 24px;
}

.modal-message {
  margin: 0;
  color: #d1d5db;
  line-height: 1.5;
  font-size: 0.95rem;
}

/* Modal Footer */
.modal-footer {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  padding: 0 24px 24px 24px;
}

.modal-footer .btn-secondary,
.modal-footer .btn-primary {
  padding: 10px 20px;
  border-radius: 8px;
  font-weight: 500;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
  min-width: 80px;
}

.modal-footer .btn-secondary {
  background: #374151;
  color: #f9fafb;
  border: 1px solid #4b5563;
}

.modal-footer .btn-secondary:hover {
  background: #4b5563;
  border-color: #6b7280;
}

.modal-footer .btn-primary {
  background: #3b82f6;
  color: white;
}

.modal-footer .btn-primary:hover {
  background: #2563eb;
}

.modal-footer .btn-danger {
  background: #ef4444;
}

.modal-footer .btn-danger:hover {
  background: #dc2626;
}

/* Responsive Design */
@media (max-width: 640px) {
  .confirmation-modal {
    margin: 16px;
    width: calc(100% - 32px);
  }
  
  .modal-header,
  .modal-body,
  .modal-footer {
    padding-left: 16px;
    padding-right: 16px;
  }
  
  .modal-footer {
    flex-direction: column-reverse;
  }
  
  .modal-footer .btn-secondary,
  .modal-footer .btn-primary {
    width: 100%;
  }
}
