import React, { useEffect } from 'react';
import { X, FileText, User, Target, GitBranch, Clock, AlertTriangle } from 'lucide-react';
import './BottomSlidePanel.css';

const BottomSlidePanel = ({ 
  isOpen, 
  onClose, 
  story,
  className = ''
}) => {
  // Handle escape key
  useEffect(() => {
    const handleEscape = (e) => {
      if (e.key === 'Escape' && isOpen) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
    };
  }, [isOpen, onClose]);

  if (!isOpen || !story) return null;

  const handleBackdropClick = (e) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'done': return <AlertTriangle className="status-icon done" size={14} />;
      case 'in_progress': return <Clock className="status-icon in-progress" size={14} />;
      case 'ready': return <Target className="status-icon ready" size={14} />;
      default: return <FileText className="status-icon backlog" size={14} />;
    }
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'critical': return '#dc2626';
      case 'high': return '#ea580c';
      case 'medium': return '#ca8a04';
      case 'low': return '#16a34a';
      default: return '#6b7280';
    }
  };

  const getImplementationStatusBadge = (status, version) => {
    switch (status) {
      case 'implemented':
        return (
          <span className="implementation-badge implemented">
            <AlertTriangle size={12} />
            Implemented
          </span>
        );
      case 'modified_after_implementation':
        return (
          <span className="implementation-badge modified">
            <AlertTriangle size={12} />
            Modified
          </span>
        );
      default:
        return (
          <span className="implementation-badge not-started">
            <Clock size={12} />
            Not Started
          </span>
        );
    }
  };

  return (
    <div className="bottom-slide-overlay" onClick={handleBackdropClick}>
      <div 
        className={`bottom-slide-panel ${className}`}
        onClick={e => e.stopPropagation()}
      >
        {/* Panel Header */}
        <div className="bottom-slide-header">
          <div className="bottom-slide-title">
            <FileText size={20} />
            <h2>Story Details</h2>
          </div>
          <button 
            className="bottom-slide-close-button"
            onClick={onClose}
            aria-label="Close panel"
          >
            <X size={20} />
          </button>
        </div>

        {/* Panel Content */}
        <div className="bottom-slide-content">
          <div className="story-details-content">
            {/* Story Header */}
            <div className="story-header">
              <div className="story-badges">
                <span className="id-badge">#{story.story_number || story.id}</span>
                {story.epic && <span className="epic-badge">{story.epic}</span>}
              </div>
              <h3 className="story-title-full">{story.title}</h3>
              <div className="story-meta">
                <div className="meta-item">
                  {getStatusIcon(story.status)}
                  <span>{story.status?.replace('_', ' ')}</span>
                </div>
                <div className="meta-item">
                  <span 
                    className="priority-badge"
                    style={{ backgroundColor: getPriorityColor(story.priority) }}
                  >
                    {story.priority}
                  </span>
                </div>
                <div className="meta-item">
                  <span className="points-badge">{story.story_points}</span>
                  <span>points</span>
                </div>
                {story.implemented_in_version && (
                  <div className="meta-item">
                    <GitBranch size={14} />
                    <span>v{story.implemented_in_version}</span>
                  </div>
                )}
              </div>
            </div>

            {/* Story Content */}
            <div className="story-content">
              <div className="content-section">
                <h4><User size={16} /> Description</h4>
                <p>{story.description || 'No description provided.'}</p>
              </div>

              <div className="content-section">
                <h4><Target size={16} /> Acceptance Criteria</h4>
                <p>{story.acceptance_criteria || 'No acceptance criteria provided.'}</p>
              </div>

              {story.business_value && (
                <div className="content-section">
                  <h4><AlertTriangle size={16} /> Business Value</h4>
                  <p>{story.business_value}</p>
                </div>
              )}

              <div className="content-section">
                <h4><Clock size={16} /> Implementation Status</h4>
                <div className="implementation-status">
                  {getImplementationStatusBadge(story.implementation_status, story.implemented_in_version)}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BottomSlidePanel;
