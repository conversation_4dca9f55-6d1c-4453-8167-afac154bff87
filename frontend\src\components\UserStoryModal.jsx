import { useState, useEffect } from 'react';
import { User, Target, FileText, AlertCircle, MessageCircle, Edit3, CheckCircle } from 'lucide-react';
import CommentTimeline from './CommentTimeline';
import AIProgressModal from './AIProgressModal';
import SlideoutPanel from './SlideoutPanel';
import './UserStoryModal.css';

const UserStoryModal = ({ isOpen, onClose, onSave, story = null, initialData = null, projectId }) => {
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    acceptance_criteria: '',
    priority: 'medium',
    story_points: 3,
    complexity_score: 3,
    epic: '',
    user_persona: 'user',
    business_value: ''
  });
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});
  const [activeTab, setActiveTab] = useState('form');
  const [showAIProgressModal, setShowAIProgressModal] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');

  useEffect(() => {
    if (story) {
      // Editing existing story
      setFormData({
        title: story.title || '',
        description: story.description || '',
        acceptance_criteria: story.acceptance_criteria || '',
        priority: story.priority || 'medium',
        story_points: story.story_points || 3,
        complexity_score: story.complexity_score || 3,
        epic: story.epic || '',
        user_persona: story.user_persona || 'user',
        business_value: story.business_value || ''
      });
    } else if (initialData) {
      // Creating new story with pre-filled data (e.g., from AI generation)
      setFormData({
        title: initialData.title || '',
        description: initialData.description || '',
        acceptance_criteria: initialData.acceptance_criteria || '',
        priority: initialData.priority || 'medium',
        story_points: initialData.story_points || 3,
        complexity_score: initialData.complexity_score || 3,
        epic: initialData.epic || '',
        user_persona: initialData.user_persona || 'user',
        business_value: initialData.business_value || ''
      });
    } else {
      // Creating new empty story
      setFormData({
        title: '',
        description: '',
        acceptance_criteria: '',
        priority: 'medium',
        story_points: 3,
        complexity_score: 3,
        epic: '',
        user_persona: 'user',
        business_value: ''
      });
    }
    setErrors({});
    setIsSubmitting(false);
    setSuccessMessage('');
  }, [story, initialData, isOpen]);

  const validateForm = () => {
    const newErrors = {};

    if (!formData.title.trim()) {
      newErrors.title = 'Story title is required';
    } else if (formData.title.trim().length < 10) {
      newErrors.title = 'Story title should be at least 10 characters long';
    }

    if (!formData.description.trim()) {
      newErrors.description = 'Story description is required';
    } else if (formData.description.trim().length < 20) {
      newErrors.description = 'Story description should be at least 20 characters long';
    }

    if (!formData.acceptance_criteria.trim()) {
      newErrors.acceptance_criteria = 'Acceptance criteria are required';
    } else if (formData.acceptance_criteria.trim().length < 20) {
      newErrors.acceptance_criteria = 'Acceptance criteria should be at least 20 characters long';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Clear field-specific error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }

    // Clear submit error and success message when user makes changes
    if (errors.submit) {
      setErrors(prev => ({
        ...prev,
        submit: ''
      }));
    }
    if (successMessage) {
      setSuccessMessage('');
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Prevent double submission
    if (isSubmitting || loading) {
      return;
    }

    if (!validateForm()) {
      return;
    }

    // Clear any previous errors and set submitting state
    setErrors({});
    setLoading(true);
    setIsSubmitting(true);

    try {
      const storyData = {
        ...formData,
        project_id: projectId,
        status: 'backlog'
      };

      const url = story
        ? `http://localhost:3000/projects/${projectId}/stories/${story.id}`
        : `http://localhost:3000/projects/${projectId}/stories`;

      const method = story ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(storyData),
      });

      if (response.ok) {
        const result = await response.json();
        setSuccessMessage(story ? 'Story updated successfully!' : 'Story created successfully!');

        // Show success message briefly before closing
        setTimeout(() => {
          onSave(result);
          onClose();
        }, 1000);
      } else {
        let errorMessage = 'Failed to save user story';
        try {
          const errorData = await response.json();
          errorMessage = errorData.error || errorMessage;
        } catch (parseError) {
          // If response isn't JSON, use status text
          errorMessage = `Server error: ${response.status} ${response.statusText}`;
        }
        setErrors({ submit: errorMessage });
      }
    } catch (error) {
      console.error('Error saving user story:', error);
      let errorMessage = 'Failed to save user story';
      if (error.name === 'TypeError' && error.message.includes('fetch')) {
        errorMessage = 'Network error: Unable to connect to server';
      } else {
        errorMessage = `Error: ${error.message}`;
      }
      setErrors({ submit: errorMessage });
    } finally {
      setLoading(false);
      setIsSubmitting(false);
    }
  };

  if (!isOpen) return null;

  return (
    <SlideoutPanel
      isOpen={isOpen}
      onClose={onClose}
      title={story ? 'Edit User Story' : (initialData ? 'Create User Story (AI Generated)' : 'Create User Story')}
      icon={FileText}
      width="800px"
      className="large"
    >
      <div className="slideout-content">
        {/* Tab Navigation - only show for existing stories */}
        {story && (
          <div className="story-modal-tabs">
            <button
              className={`tab-button ${activeTab === 'form' ? 'active' : ''}`}
              onClick={() => setActiveTab('form')}
            >
              <Edit3 size={16} />
              Edit Story
            </button>
            <button
              className={`tab-button ${activeTab === 'progress' ? 'active' : ''}`}
              onClick={() => setActiveTab('progress')}
            >
              <MessageCircle size={16} />
              AI Progress
            </button>
          </div>
        )}

        <div className="story-modal-content">
          {/* Form Tab */}
          {activeTab === 'form' && (
            <form onSubmit={handleSubmit}>
            <div className="story-form-group">
            <label htmlFor="title">Story Title *</label>
            <input
              type="text"
              id="title"
              name="title"
              value={formData.title}
              onChange={handleChange}
              className={errors.title ? 'error' : ''}
              placeholder="As a [user], I want [goal] so that [benefit]"
            />
            {errors.title && <span className="error-message">{errors.title}</span>}
          </div>

          <div className="story-form-row">
            <div className="story-form-group">
              <label htmlFor="user_persona">User Persona</label>
              <select
                id="user_persona"
                name="user_persona"
                value={formData.user_persona}
                onChange={handleChange}
              >
                <option value="user">End User</option>
                <option value="admin">Administrator</option>
                <option value="customer">Customer</option>
                <option value="developer">Developer</option>
                <option value="manager">Manager</option>
              </select>
            </div>

            <div className="story-form-group">
              <label htmlFor="epic">Epic (Optional)</label>
              <input
                type="text"
                id="epic"
                name="epic"
                value={formData.epic}
                onChange={handleChange}
                placeholder="e.g., User Authentication"
              />
            </div>
          </div>
          
          <div className="story-form-group">
            <label htmlFor="description">Description *</label>
            <textarea
              id="description"
              name="description"
              value={formData.description}
              onChange={handleChange}
              className={errors.description ? 'error' : ''}
              placeholder="Detailed description of what the user wants to accomplish..."
              rows={4}
            />
            {errors.description && <span className="error-message">{errors.description}</span>}
          </div>
          
          <div className="story-form-group">
            <label htmlFor="acceptance_criteria">Acceptance Criteria *</label>
            <textarea
              id="acceptance_criteria"
              name="acceptance_criteria"
              value={formData.acceptance_criteria}
              onChange={handleChange}
              className={errors.acceptance_criteria ? 'error' : ''}
              placeholder="Given [context], When [action], Then [outcome]..."
              rows={4}
            />
            {errors.acceptance_criteria && <span className="error-message">{errors.acceptance_criteria}</span>}
          </div>

          <div className="story-form-group">
            <label htmlFor="business_value">Business Value</label>
            <textarea
              id="business_value"
              name="business_value"
              value={formData.business_value}
              onChange={handleChange}
              placeholder="Why is this story important? What business value does it provide?"
              rows={2}
            />
          </div>
          
          <div className="story-form-row">
            <div className="story-form-group">
              <label htmlFor="priority">Priority</label>
              <select
                id="priority"
                name="priority"
                value={formData.priority}
                onChange={handleChange}
              >
                <option value="low">Low</option>
                <option value="medium">Medium</option>
                <option value="high">High</option>
                <option value="critical">Critical</option>
              </select>
            </div>

            <div className="story-form-group">
              <label htmlFor="story_points">Story Points</label>
              <select
                id="story_points"
                name="story_points"
                value={formData.story_points}
                onChange={handleChange}
              >
                <option value="1">1 - Very Small</option>
                <option value="2">2 - Small</option>
                <option value="3">3 - Medium</option>
                <option value="5">5 - Large</option>
                <option value="8">8 - Very Large</option>
                <option value="13">13 - Extra Large</option>
              </select>
            </div>

            <div className="story-form-group">
              <label htmlFor="complexity_score">Complexity Score</label>
              <select
                id="complexity_score"
                name="complexity_score"
                value={formData.complexity_score}
                onChange={handleChange}
                title="Flow-based complexity scoring for agentic development"
              >
                <option value="1">XS (1) - Trivial changes, config updates</option>
                <option value="2">S (2) - Small features, bug fixes</option>
                <option value="3">M (3) - Medium features (default)</option>
                <option value="5">L (5) - Large features, complex logic</option>
                <option value="8">XL (8) - Very large features, major refactoring</option>
                <option value="13">XXL (13) - Epic-level work, should be broken down</option>
              </select>
            </div>
          </div>

          {errors.submit && (
            <div className="error-banner">
              <AlertCircle size={16} />
              {errors.submit}
            </div>
          )}

          {successMessage && (
            <div className="success-banner">
              <CheckCircle size={16} />
              {successMessage}
            </div>
          )}
          
              <div className="story-modal-actions">
                <button type="button" className="btn-secondary" onClick={onClose}>
                  Cancel
                </button>
                <button type="submit" className="btn-primary" disabled={loading || isSubmitting}>
                  {loading || isSubmitting ? 'Saving...' : (story ? 'Update Story' : 'Create Story')}
                </button>
              </div>
            </form>
          )}

          {/* AI Progress Tab */}
          {activeTab === 'progress' && story && (
            <div className="progress-tab-content">
              <div className="progress-header">
                <h3>AI Development Progress</h3>
                <p>Track AI agent activities and decisions for this user story</p>
              </div>

              <div className="progress-preview">
                <div className="preview-message">
                  <MessageCircle size={48} />
                  <h4>View AI Progress in Detail</h4>
                  <p>Open the full AI progress viewer to see detailed comments, decisions, and timeline in a dedicated interface with better readability.</p>

                  <button
                    type="button"
                    className="btn-primary open-progress-btn"
                    onClick={() => setShowAIProgressModal(true)}
                  >
                    <MessageCircle size={16} />
                    Open AI Progress Viewer
                  </button>
                </div>
              </div>

              <div className="progress-actions">
                <button type="button" className="btn-secondary" onClick={onClose}>
                  Close
                </button>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* AI Progress Modal */}
      <AIProgressModal
        isOpen={showAIProgressModal}
        onClose={() => setShowAIProgressModal(false)}
        projectId={projectId}
        storyId={story?.id}
        storyTitle={story?.title}
      />
    </SlideoutPanel>
  );
};

export default UserStoryModal;
