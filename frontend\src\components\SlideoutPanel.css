/* Slideout Panel Styles */
.slideout-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: stretch;
  justify-content: flex-end;
  z-index: 1100;
  backdrop-filter: blur(4px);
}

.slideout-panel {
  background: #1a1a1a;
  border-left: 1px solid #374151;
  display: flex;
  flex-direction: column;
  color: #e5e5e5;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  box-shadow: -10px 0 30px rgba(0, 0, 0, 0.3);
  animation: slideInFromRight 0.3s ease-out;
  max-width: 90vw;
  height: 100vh;
  overflow: hidden;
}

@keyframes slideInFromRight {
  from {
    transform: translateX(100%);
  }
  to {
    transform: translateX(0);
  }
}

/* Panel Header */
.slideout-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #374151;
  background: #2d2d2d;
  flex-shrink: 0;
}

.slideout-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.slideout-title h2 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #ffffff;
}

.slideout-close-button {
  background: none;
  border: none;
  color: #9ca3af;
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.slideout-close-button:hover {
  background: #374151;
  color: #e5e5e5;
}

/* Panel Content */
.slideout-content {
  flex: 1;
  overflow-y: auto;
  padding: 0;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .slideout-panel {
    max-width: 95vw;
  }
}

@media (max-width: 768px) {
  .slideout-panel {
    width: 100vw !important;
    max-width: 100vw;
  }
  
  .slideout-header {
    padding: 16px 20px;
  }
  
  .slideout-title h2 {
    font-size: 16px;
  }
}

/* Specific panel sizes */
.slideout-panel.small {
  width: 400px;
}

.slideout-panel.medium {
  width: 600px;
}

.slideout-panel.large {
  width: 800px;
}

.slideout-panel.extra-large {
  width: 1200px;
}

/* Panel content padding variants */
.slideout-content.padded {
  padding: 24px;
}

.slideout-content.form-content {
  padding: 0;
}

.slideout-content.form-content > form {
  padding: 24px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* Scrollbar styling for slideout content */
.slideout-content::-webkit-scrollbar {
  width: 8px;
}

.slideout-content::-webkit-scrollbar-track {
  background: transparent;
}

.slideout-content::-webkit-scrollbar-thumb {
  background: #4b5563;
  border-radius: 4px;
}

.slideout-content::-webkit-scrollbar-thumb:hover {
  background: #6b7280;
}
