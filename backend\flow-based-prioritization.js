/**
 * Flow-Based Prioritization System
 * Replaces time-based capacity with flow metrics for story selection
 * Implements WIP limits and flow-optimized algorithms
 */

const db = require('./database');
const { COMPLEXITY_SCORES } = require('./flow-metrics');

class FlowBasedPrioritizer {
  constructor() {
    this.wipLimits = {
      // Maximum concurrent stories per status
      in_progress: 3,
      
      // Maximum complexity in progress per project
      maxComplexityInProgress: 13, // XXL complexity limit
      
      // Minimum complexity for batch processing
      minBatchComplexity: 5,
      
      // Maximum stories to select per tick
      maxStoriesPerTick: 3
    };
  }

  /**
   * Select stories for processing based on flow metrics
   */
  async selectStoriesForProcessing(projectId, maxStories = 3) {
    try {
      // Check current WIP
      const currentWip = await this.getCurrentWIP(projectId);
      
      if (currentWip.inProgressCount >= this.wipLimits.in_progress) {
        return {
          stories: [],
          reason: `WIP limit reached: ${currentWip.inProgressCount}/${this.wipLimits.in_progress} stories in progress`,
          wipStatus: currentWip
        };
      }

      if (currentWip.complexityInProgress >= this.wipLimits.maxComplexityInProgress) {
        return {
          stories: [],
          reason: `Complexity WIP limit reached: ${currentWip.complexityInProgress}/${this.wipLimits.maxComplexityInProgress}`,
          wipStatus: currentWip
        };
      }

      // Calculate available capacity
      const availableSlots = this.wipLimits.in_progress - currentWip.inProgressCount;
      const availableComplexity = this.wipLimits.maxComplexityInProgress - currentWip.complexityInProgress;
      const maxToSelect = Math.min(maxStories, availableSlots);

      // Get candidate stories using flow-based prioritization
      const candidates = await this.getCandidateStories(projectId, availableComplexity);
      
      if (candidates.length === 0) {
        return {
          stories: [],
          reason: 'No eligible stories found for processing',
          wipStatus: currentWip
        };
      }

      // Select optimal batch using flow principles
      const selectedStories = this.selectOptimalBatch(candidates, maxToSelect, availableComplexity);

      return {
        stories: selectedStories,
        reason: `Selected ${selectedStories.length} stories based on flow optimization`,
        wipStatus: currentWip,
        metrics: {
          totalComplexity: selectedStories.reduce((sum, s) => sum + s.complexity_score, 0),
          averageComplexity: selectedStories.length > 0 
            ? selectedStories.reduce((sum, s) => sum + s.complexity_score, 0) / selectedStories.length 
            : 0
        }
      };

    } catch (error) {
      console.error('Error selecting stories for processing:', error);
      return {
        stories: [],
        reason: 'Error in story selection',
        error: error.message
      };
    }
  }

  /**
   * Get current Work In Progress metrics
   */
  async getCurrentWIP(projectId) {
    return new Promise((resolve, reject) => {
      db.all(
        `SELECT status, complexity_score, COUNT(*) as count, SUM(complexity_score) as total_complexity
         FROM user_stories 
         WHERE project_id = ? AND status IN ('in_progress', 'backlog')
         GROUP BY status`,
        [projectId],
        (err, rows) => {
          if (err) {
            reject(err);
            return;
          }

          const result = {
            inProgressCount: 0,
            complexityInProgress: 0,
            backlogCount: 0,
            backlogComplexity: 0
          };

          rows.forEach(row => {
            if (row.status === 'in_progress') {
              result.inProgressCount = row.count;
              result.complexityInProgress = row.total_complexity || 0;
            } else if (row.status === 'backlog') {
              result.backlogCount = row.count;
              result.backlogComplexity = row.total_complexity || 0;
            }
          });

          resolve(result);
        }
      );
    });
  }

  /**
   * Get candidate stories for processing using flow-based criteria
   */
  async getCandidateStories(projectId, availableComplexity) {
    return new Promise((resolve, reject) => {
      db.all(
        `SELECT us.*, s.status as sprint_status
         FROM user_stories us
         LEFT JOIN sprints s ON us.sprint_id = s.id
         WHERE us.project_id = ? 
         AND us.status = 'backlog'
         AND us.complexity_score <= ?
         AND (s.status = 'active' OR s.status IS NULL)
         ORDER BY 
           CASE us.priority 
             WHEN 'high' THEN 1 
             WHEN 'medium' THEN 2 
             WHEN 'low' THEN 3 
             ELSE 4 
           END,
           us.complexity_score ASC,
           us.created_at ASC
         LIMIT 20`,
        [projectId, availableComplexity],
        (err, rows) => {
          if (err) reject(err);
          else resolve(rows || []);
        }
      );
    });
  }

  /**
   * Select optimal batch of stories using flow principles
   */
  selectOptimalBatch(candidates, maxStories, availableComplexity) {
    if (candidates.length === 0) return [];

    const selected = [];
    let remainingComplexity = availableComplexity;
    let remainingSlots = maxStories;

    // Priority 1: High priority stories that fit
    const highPriority = candidates.filter(s => s.priority === 'high');
    for (const story of highPriority) {
      if (remainingSlots > 0 && story.complexity_score <= remainingComplexity) {
        selected.push(story);
        remainingComplexity -= story.complexity_score;
        remainingSlots--;
      }
    }

    // Priority 2: Fill remaining capacity with medium priority stories
    const mediumPriority = candidates.filter(s => 
      s.priority === 'medium' && !selected.find(sel => sel.id === s.id)
    );
    
    for (const story of mediumPriority) {
      if (remainingSlots > 0 && story.complexity_score <= remainingComplexity) {
        selected.push(story);
        remainingComplexity -= story.complexity_score;
        remainingSlots--;
      }
    }

    // Priority 3: Low priority stories if there's still capacity
    const lowPriority = candidates.filter(s => 
      s.priority === 'low' && !selected.find(sel => sel.id === s.id)
    );
    
    for (const story of lowPriority) {
      if (remainingSlots > 0 && story.complexity_score <= remainingComplexity) {
        selected.push(story);
        remainingComplexity -= story.complexity_score;
        remainingSlots--;
      }
    }

    return selected;
  }

  /**
   * Check if project is ready for new work based on flow metrics
   */
  async isProjectReadyForWork(projectId) {
    try {
      const wip = await this.getCurrentWIP(projectId);
      
      // Check WIP limits
      if (wip.inProgressCount >= this.wipLimits.in_progress) {
        return {
          ready: false,
          reason: 'WIP limit reached',
          metrics: wip
        };
      }

      if (wip.complexityInProgress >= this.wipLimits.maxComplexityInProgress) {
        return {
          ready: false,
          reason: 'Complexity WIP limit reached',
          metrics: wip
        };
      }

      // Check if there are eligible stories
      const candidates = await this.getCandidateStories(
        projectId, 
        this.wipLimits.maxComplexityInProgress - wip.complexityInProgress
      );

      if (candidates.length === 0) {
        return {
          ready: false,
          reason: 'No eligible stories in backlog',
          metrics: wip
        };
      }

      return {
        ready: true,
        reason: 'Project ready for new work',
        metrics: wip,
        availableStories: candidates.length
      };

    } catch (error) {
      console.error('Error checking project readiness:', error);
      return {
        ready: false,
        reason: 'Error checking project status',
        error: error.message
      };
    }
  }

  /**
   * Get flow metrics summary for a project
   */
  async getFlowMetricsSummary(projectId) {
    try {
      const wip = await this.getCurrentWIP(projectId);
      const candidates = await this.getCandidateStories(projectId, 100); // Get all candidates
      
      return {
        wip,
        backlogHealth: {
          totalStories: candidates.length,
          highPriority: candidates.filter(s => s.priority === 'high').length,
          mediumPriority: candidates.filter(s => s.priority === 'medium').length,
          lowPriority: candidates.filter(s => s.priority === 'low').length,
          averageComplexity: candidates.length > 0 
            ? candidates.reduce((sum, s) => sum + s.complexity_score, 0) / candidates.length 
            : 0
        },
        wipLimits: this.wipLimits,
        flowHealth: this.calculateFlowHealth(wip)
      };
    } catch (error) {
      console.error('Error getting flow metrics summary:', error);
      return null;
    }
  }

  /**
   * Calculate flow health score
   */
  calculateFlowHealth(wip) {
    const wipUtilization = wip.inProgressCount / this.wipLimits.in_progress;
    const complexityUtilization = wip.complexityInProgress / this.wipLimits.maxComplexityInProgress;
    
    // Optimal utilization is around 70-80%
    const optimalWipUtilization = Math.abs(wipUtilization - 0.75) < 0.1;
    const optimalComplexityUtilization = Math.abs(complexityUtilization - 0.75) < 0.1;
    
    let score = 100;
    
    // Penalize over-utilization (>90%)
    if (wipUtilization > 0.9) score -= 30;
    if (complexityUtilization > 0.9) score -= 30;
    
    // Penalize under-utilization (<50%)
    if (wipUtilization < 0.5) score -= 20;
    if (complexityUtilization < 0.5) score -= 20;
    
    // Bonus for optimal utilization
    if (optimalWipUtilization) score += 10;
    if (optimalComplexityUtilization) score += 10;
    
    return Math.max(0, Math.min(100, score));
  }
}

module.exports = new FlowBasedPrioritizer();
