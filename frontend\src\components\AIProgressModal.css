/* AI Progress Modal - Updated for Slideout */
.ai-progress-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.ai-progress-modal {
  background: #1a1a1a;
  border: 1px solid #374151;
  border-radius: 12px;
  width: 90vw;
  max-width: 1200px;
  height: 85vh;
  display: flex;
  flex-direction: column;
  color: #e5e5e5;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
}

.ai-progress-modal-content {
  display: flex;
  flex-direction: column;
  height: 100%;
}

/* Header */
.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #374151;
  background: #2d2d2d;
  border-radius: 12px 12px 0 0;
}

.modal-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.modal-title h2 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #ffffff;
}

.story-title {
  margin: 4px 0 0 0;
  font-size: 14px;
  color: #9ca3af;
  font-weight: 400;
}

.close-button {
  background: none;
  border: none;
  color: #9ca3af;
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  transition: all 0.2s;
}

.close-button:hover {
  background: #374151;
  color: #e5e5e5;
}

/* Stats Bar */
.stats-bar {
  display: flex;
  gap: 24px;
  padding: 16px 24px;
  background: #2d2d2d;
  border-bottom: 1px solid #374151;
}

.stat-item {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.stat-label {
  font-size: 12px;
  color: #9ca3af;
  font-weight: 400;
}

.stat-value {
  font-size: 18px;
  font-weight: 600;
  color: #e5e5e5;
}

.stat-value.warning {
  color: #f59e0b;
}

/* Filters */
.filters-section {
  display: flex;
  gap: 20px;
  align-items: center;
  padding: 16px 24px;
  background: #1a1a1a;
  border-bottom: 1px solid #374151;
  flex-wrap: wrap;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-group label {
  font-size: 13px;
  color: #e5e5e5;
  font-weight: 500;
  white-space: nowrap;
}

.filter-group select {
  padding: 6px 10px;
  border: 1px solid #374151;
  border-radius: 6px;
  background: #2d2d2d;
  color: #e5e5e5;
  font-size: 13px;
  min-width: 120px;
}

.filter-group select:focus {
  outline: none;
  border-color: #4b5563;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 6px;
  cursor: pointer;
}

.checkbox-label input[type="checkbox"] {
  accent-color: #2563eb;
}

/* Content */
.modal-content {
  flex: 1;
  overflow-y: auto;
  /* padding: 24px; */
  background: #1a1a1a;
}

.loading-state, .error-state, .empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  text-align: center;
  gap: 16px;
}

.spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #374151;
  border-top: 3px solid #2563eb;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-state {
  color: #f87171;
}

.retry-button {
  padding: 8px 16px;
  background: #2563eb;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 13px;
  font-weight: 500;
}

.retry-button:hover {
  background: #1d4ed8;
}

.empty-state {
  color: #9ca3af;
}

.empty-state h3 {
  margin: 0;
  color: #d1d5db;
  font-size: 18px;
  font-weight: 600;
}

.empty-state p {
  margin: 0;
  font-size: 14px;
}

/* Timeline */
.comments-timeline {
  position: relative;
  max-width: 800px;
  margin: 0 auto;
}

.comments-timeline::before {
  content: '';
  position: absolute;
  left: 20px;
  top: 0;
  bottom: 0;
  width: 2px;
  background: #374151;
}

.timeline-item {
  position: relative;
  display: flex;
  margin-bottom: 24px;
  padding-left: 16px;
}

.timeline-item:last-child {
  margin-bottom: 0;
}

.timeline-marker {
  position: relative;
  z-index: 1;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #2d2d2d;
  border: 2px solid #374151;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  flex-shrink: 0;
}

.timeline-content {
  flex: 1;
  background: #2d2d2d;
  border: 1px solid #374151;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.comment-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
  flex-wrap: wrap;
  gap: 8px;
}

.comment-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.author-name {
  font-weight: 600;
  color: #ffffff;
  font-size: 14px;
}

.comment-type-badge {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 11px;
  text-transform: uppercase;
  font-weight: 500;
}

.comment-type-badge.progress {
  background: #1e3a8a;
  color: #60a5fa;
}

.comment-type-badge.decision {
  background: #065f46;
  color: #34d399;
}

.comment-type-badge.blocker {
  background: #7f1d1d;
  color: #f87171;
}

.comment-type-badge.update {
  background: #581c87;
  color: #a78bfa;
}

.priority-badge {
  padding: 2px 6px;
  border-radius: 12px;
  font-size: 10px;
  text-transform: uppercase;
  font-weight: 600;
}

.priority-badge.high {
  background: #f59e0b;
  color: #1f2937;
}

.priority-badge.critical {
  background: #dc2626;
  color: white;
}

.internal-badge {
  background: #374151;
  color: #9ca3af;
  padding: 2px 6px;
  border-radius: 12px;
  font-size: 10px;
  text-transform: uppercase;
  font-weight: 500;
}

.comment-time {
  font-size: 12px;
  color: #9ca3af;
  white-space: nowrap;
}

.comment-body {
  color: #e5e5e5;
  line-height: 1.6;
}

.comment-text {
  margin: 0;
}

.decision-format {
  background: #374151;
  border-left: 4px solid #10b981;
  padding: 12px 16px;
  border-radius: 6px;
}

.decision-title {
  margin-bottom: 8px;
  color: #ffffff;
}

.decision-reasoning {
  color: #d1d5db;
  font-size: 14px;
}

.comment-metadata {
  margin-top: 12px;
  font-size: 12px;
}

.comment-metadata summary {
  cursor: pointer;
  color: #9ca3af;
  font-weight: 500;
  padding: 4px 0;
}

.comment-metadata pre {
  background: #374151;
  color: #e5e5e5;
  padding: 12px;
  border-radius: 6px;
  margin-top: 8px;
  font-size: 11px;
  overflow-x: auto;
  border: 1px solid #4b5563;
}

/* Timeline item type styling */
.timeline-item.blocker .timeline-marker {
  border-color: #dc2626;
  background: #7f1d1d;
  color: #f87171;
}

.timeline-item.decision .timeline-marker {
  border-color: #10b981;
  background: #065f46;
  color: #34d399;
}

.timeline-item.progress .timeline-marker {
  border-color: #2563eb;
  background: #1e3a8a;
  color: #60a5fa;
}

.timeline-item.update .timeline-marker {
  border-color: #7c3aed;
  background: #581c87;
  color: #a78bfa;
}

/* Footer */
.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 16px 24px;
  border-top: 1px solid #374151;
  background: #2d2d2d;
  border-radius: 0 0 12px 12px;
}

.btn-primary, .btn-secondary {
  padding: 8px 16px;
  border: 1px solid transparent;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  font-size: 13px;
  transition: all 0.2s;
}

.btn-primary {
  background: #2563eb;
  color: white;
  border-color: #2563eb;
}

.btn-primary:hover {
  background: #1d4ed8;
  border-color: #1d4ed8;
}

.btn-secondary {
  background: transparent;
  color: #e5e5e5;
  border-color: #374151;
}

.btn-secondary:hover {
  background: #374151;
}

/* Responsive */
@media (max-width: 768px) {
  .ai-progress-modal {
    width: 95vw;
    height: 90vh;
  }
  
  .modal-header {
    padding: 16px 20px;
  }
  
  .stats-bar {
    flex-wrap: wrap;
    gap: 16px;
    padding: 12px 20px;
  }
  
  .filters-section {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
    padding: 12px 20px;
  }
  
  .filter-group {
    justify-content: space-between;
  }
  
  .modal-content {
    padding: 16px 20px;
  }
  
  .timeline-marker {
    width: 32px;
    height: 32px;
  }
  
  .comment-header {
    flex-direction: column;
    align-items: flex-start;
  }
}
