.tasks-section {
  margin: 16px 0;
  border: 1px solid #374151;
  border-radius: 8px;
  background: #1f2937;
}

.tasks-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  cursor: pointer;
  border-bottom: 1px solid #374151;
  transition: background-color 0.2s;
}

.tasks-header:hover {
  background: #374151;
}

.tasks-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.tasks-title h4 {
  margin: 0;
  color: #f9fafb;
  font-size: 14px;
  font-weight: 600;
}

.tasks-summary {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 12px;
  color: #9ca3af;
}

.tasks-progress {
  color: #10b981;
  font-weight: 500;
}

.tasks-hours {
  color: #f59e0b;
  font-weight: 500;
}

.tasks-list {
  padding: 0;
}

.task-item {
  padding: 12px 16px;
  border-bottom: 1px solid #374151;
  transition: background-color 0.2s;
}

.task-item:last-child {
  border-bottom: none;
}

.task-item:hover {
  background: #374151;
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.task-status {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.task-title {
  color: #f9fafb;
  font-size: 13px;
  font-weight: 500;
  line-height: 1.4;
}

.task-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
}

.task-type {
  padding: 2px 6px;
  border-radius: 4px;
  color: white;
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.task-hours {
  color: #f59e0b;
  font-size: 11px;
  font-weight: 500;
}

.task-description {
  color: #9ca3af;
  font-size: 12px;
  line-height: 1.4;
  margin-bottom: 8px;
  padding-left: 22px;
}

.task-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 11px;
  color: #6b7280;
  padding-left: 22px;
}

.task-assignee {
  display: flex;
  align-items: center;
  gap: 4px;
}

.task-dates {
  color: #6b7280;
}

/* Status Icons */
.status-done {
  color: #10b981;
}

.status-in-progress {
  color: #f59e0b;
}

.status-todo {
  color: #6b7280;
}

/* Priority Icons */
.priority-critical {
  color: #ef4444;
}

.priority-high {
  color: #f97316;
}

.priority-medium {
  color: #f59e0b;
}

.priority-low {
  color: #6b7280;
}

/* Loading and Error States */
.loading-spinner {
  color: #9ca3af;
  font-size: 12px;
}

.error-text {
  color: #ef4444;
  font-size: 12px;
}

.no-tasks {
  color: #6b7280;
  font-size: 12px;
  font-style: italic;
}

/* Responsive Design */
@media (max-width: 768px) {
  .task-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .task-meta {
    align-self: flex-end;
  }
  
  .task-footer {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}
