/**
 * Comprehensive AI Model Configuration
 * Contains detailed information about available models, their costs, capabilities, and optimal use cases
 */

const AI_MODELS = {
  // Anthropic Claude Models
  'anthropic/claude-3-opus': {
    name: 'Claude 3 Opus',
    provider: 'Anthropic',
    tier: 'premium',
    costPer1kTokens: {
      input: 0.015,   // $15 per 1M tokens
      output: 0.075   // $75 per 1M tokens
    },
    contextWindow: 200000,
    capabilities: {
      reasoning: 10,
      codeGeneration: 10,
      creativity: 10,
      textProcessing: 10,
      complexAnalysis: 10,
      multiStep: 10
    },
    speed: 'slow',
    reliability: 10,
    bestUseCases: [
      'Complex code architecture',
      'Advanced reasoning tasks',
      'Creative problem solving',
      'Multi-step analysis',
      'Critical business logic',
      'Complex acceptance criteria validation'
    ],
    description: 'Most capable model, highest cost. Use only for the most complex tasks requiring top-tier reasoning.'
  },

  'anthropic/claude-sonnet-4': {
    name: 'Claude Sonnet 4',
    provider: 'Anthropic',
    tier: 'balanced',
    costPer1kTokens: {
      input: 0.003,   // $3 per 1M tokens
      output: 0.015   // $15 per 1M tokens
    },
    contextWindow: 200000,
    capabilities: {
      reasoning: 9,
      codeGeneration: 9,
      creativity: 9,
      textProcessing: 9,
      complexAnalysis: 9,
      multiStep: 9
    },
    speed: 'medium',
    reliability: 9,
    bestUseCases: [
      'Standard code generation',
      'User story refinement',
      'Task breakdown',
      'Code review',
      'Documentation generation',
      'Medium complexity analysis'
    ],
    description: 'Latest Claude Sonnet with enhanced capabilities. Excellent balance of capability and cost.'
  },

  'anthropic/claude-3-haiku': {
    name: 'Claude 3 Haiku',
    provider: 'Anthropic',
    tier: 'efficient',
    costPer1kTokens: {
      input: 0.00025, // $0.25 per 1M tokens
      output: 0.00125 // $1.25 per 1M tokens
    },
    contextWindow: 200000,
    capabilities: {
      reasoning: 6,
      codeGeneration: 6,
      creativity: 6,
      textProcessing: 8,
      complexAnalysis: 5,
      multiStep: 5
    },
    speed: 'fast',
    reliability: 8,
    bestUseCases: [
      'Simple text processing',
      'Basic code snippets',
      'Task analysis',
      'Status updates',
      'Simple validation',
      'Content formatting'
    ],
    description: 'Fast and cost-effective. Perfect for simple tasks and high-volume processing.'
  },

  // OpenAI Models
  'openai/gpt-5': {
    name: 'GPT-5',
    provider: 'OpenAI',
    tier: 'premium',
    costPer1kTokens: {
      input: 0.01,    // $10 per 1M tokens (estimated)
      output: 0.03    // $30 per 1M tokens (estimated)
    },
    contextWindow: 200000,
    capabilities: {
      reasoning: 10,
      codeGeneration: 10,
      creativity: 10,
      textProcessing: 10,
      complexAnalysis: 10,
      multiStep: 10
    },
    speed: 'medium',
    reliability: 9,
    bestUseCases: [
      'Most complex reasoning tasks',
      'Advanced code architecture',
      'Creative problem solving',
      'Multi-step analysis',
      'Critical business logic',
      'Research and development'
    ],
    description: 'Latest GPT-5 model with state-of-the-art capabilities for the most demanding tasks.'
  },

  'openai/gpt-5-mini': {
    name: 'GPT-5 Mini',
    provider: 'OpenAI',
    tier: 'balanced',
    costPer1kTokens: {
      input: 0.002,   // $2 per 1M tokens (estimated)
      output: 0.008   // $8 per 1M tokens (estimated)
    },
    contextWindow: 128000,
    capabilities: {
      reasoning: 8,
      codeGeneration: 8,
      creativity: 8,
      textProcessing: 9,
      complexAnalysis: 8,
      multiStep: 8
    },
    speed: 'fast',
    reliability: 9,
    bestUseCases: [
      'Standard code generation',
      'Advanced text processing',
      'Medium complexity analysis',
      'Code review and optimization',
      'Documentation generation'
    ],
    description: 'Balanced GPT-5 variant offering excellent performance at moderate cost.'
  },

  'openai/gpt-5-nano': {
    name: 'GPT-5 Nano',
    provider: 'OpenAI',
    tier: 'efficient',
    costPer1kTokens: {
      input: 0.0005,  // $0.50 per 1M tokens (estimated)
      output: 0.002   // $2 per 1M tokens (estimated)
    },
    contextWindow: 64000,
    capabilities: {
      reasoning: 7,
      codeGeneration: 7,
      creativity: 7,
      textProcessing: 8,
      complexAnalysis: 6,
      multiStep: 6
    },
    speed: 'very-fast',
    reliability: 8,
    bestUseCases: [
      'Simple code generation',
      'Text processing',
      'Basic analysis',
      'Content generation',
      'High-volume tasks'
    ],
    description: 'Ultra-efficient GPT-5 variant for high-volume, cost-sensitive tasks.'
  },

  'openai/gpt-4o': {
    name: 'GPT-4o',
    provider: 'OpenAI',
    tier: 'premium',
    costPer1kTokens: {
      input: 0.0025,  // $2.50 per 1M tokens
      output: 0.01    // $10 per 1M tokens
    },
    contextWindow: 128000,
    capabilities: {
      reasoning: 9,
      codeGeneration: 9,
      creativity: 9,
      textProcessing: 9,
      complexAnalysis: 9,
      multiStep: 9
    },
    speed: 'medium',
    reliability: 9,
    bestUseCases: [
      'Complex code generation',
      'Advanced reasoning',
      'Multi-modal tasks',
      'Complex analysis',
      'Architecture decisions'
    ],
    description: 'Latest GPT-4 model with improved capabilities and cost efficiency.'
  },

  'openai/gpt-4o-mini': {
    name: 'GPT-4o Mini',
    provider: 'OpenAI',
    tier: 'efficient',
    costPer1kTokens: {
      input: 0.00015, // $0.15 per 1M tokens
      output: 0.0006  // $0.60 per 1M tokens
    },
    contextWindow: 128000,
    capabilities: {
      reasoning: 7,
      codeGeneration: 7,
      creativity: 7,
      textProcessing: 8,
      complexAnalysis: 7,
      multiStep: 7
    },
    speed: 'fast',
    reliability: 8,
    bestUseCases: [
      'Simple code generation',
      'Text processing',
      'Basic analysis',
      'Content generation',
      'Simple reasoning tasks'
    ],
    description: 'Cost-effective GPT-4 variant for simpler tasks with good performance.'
  },

  // Meta Llama Models
  'meta-llama/llama-3.1-405b-instruct': {
    name: 'Llama 3.1 405B Instruct',
    provider: 'Meta',
    tier: 'premium',
    costPer1kTokens: {
      input: 0.005,   // $5 per 1M tokens
      output: 0.015   // $15 per 1M tokens
    },
    contextWindow: 131072,
    capabilities: {
      reasoning: 9,
      codeGeneration: 8,
      creativity: 8,
      textProcessing: 9,
      complexAnalysis: 9,
      multiStep: 8
    },
    speed: 'slow',
    reliability: 8,
    bestUseCases: [
      'Complex reasoning',
      'Large-scale analysis',
      'Open-source alternative to premium models',
      'Research and development'
    ],
    description: 'Largest open-source model with excellent capabilities for complex tasks.'
  },

  'meta-llama/llama-3.1-70b-instruct': {
    name: 'Llama 3.1 70B Instruct',
    provider: 'Meta',
    tier: 'balanced',
    costPer1kTokens: {
      input: 0.0009,  // $0.90 per 1M tokens
      output: 0.0009  // $0.90 per 1M tokens
    },
    contextWindow: 131072,
    capabilities: {
      reasoning: 7,
      codeGeneration: 7,
      creativity: 7,
      textProcessing: 8,
      complexAnalysis: 7,
      multiStep: 7
    },
    speed: 'medium',
    reliability: 8,
    bestUseCases: [
      'Open-source alternative',
      'Code generation',
      'Text analysis',
      'General reasoning'
    ],
    description: 'Good open-source alternative with reasonable costs and capabilities.'
  },

  'meta-llama/llama-3-8b-instruct': {
    name: 'Llama 3 8B',
    provider: 'Meta',
    tier: 'budget',
    costPer1kTokens: {
      input: 0.0002,  // $0.20 per 1M tokens
      output: 0.0002  // $0.20 per 1M tokens
    },
    contextWindow: 8192,
    capabilities: {
      reasoning: 5,
      codeGeneration: 5,
      creativity: 5,
      textProcessing: 7,
      complexAnalysis: 4,
      multiStep: 4
    },
    speed: 'very-fast',
    reliability: 7,
    bestUseCases: [
      'Simple text tasks',
      'Basic code snippets',
      'Content formatting',
      'Simple analysis'
    ],
    description: 'Ultra-low cost option for very simple tasks.'
  },

  // Mistral Models
  'mistralai/mistral-large': {
    name: 'Mistral Large',
    provider: 'Mistral AI',
    tier: 'premium',
    costPer1kTokens: {
      input: 0.008,   // $8 per 1M tokens
      output: 0.024   // $24 per 1M tokens
    },
    contextWindow: 32768,
    capabilities: {
      reasoning: 8,
      codeGeneration: 8,
      creativity: 7,
      textProcessing: 8,
      complexAnalysis: 8,
      multiStep: 8
    },
    speed: 'medium',
    reliability: 8,
    bestUseCases: [
      'European data compliance',
      'Code generation',
      'Complex reasoning',
      'Multi-language tasks'
    ],
    description: 'Strong European alternative with good capabilities and compliance features.'
  },

  'mistralai/mistral-medium': {
    name: 'Mistral Medium',
    provider: 'Mistral AI',
    tier: 'balanced',
    costPer1kTokens: {
      input: 0.0027,  // $2.70 per 1M tokens
      output: 0.0081  // $8.10 per 1M tokens
    },
    contextWindow: 32768,
    capabilities: {
      reasoning: 7,
      codeGeneration: 7,
      creativity: 6,
      textProcessing: 7,
      complexAnalysis: 7,
      multiStep: 7
    },
    speed: 'medium',
    reliability: 8,
    bestUseCases: [
      'Balanced tasks',
      'Code generation',
      'Analysis',
      'European compliance needs'
    ],
    description: 'Good balance of cost and capability with European focus.'
  }
};

// Task complexity definitions
const TASK_COMPLEXITY = {
  SIMPLE: {
    level: 1,
    description: 'Basic text processing, simple formatting, status updates',
    examples: ['Format text', 'Simple validation', 'Status updates', 'Basic content generation']
  },
  MEDIUM: {
    level: 2,
    description: 'Standard development tasks, user story refinement, basic code generation',
    examples: ['User story refinement', 'Task breakdown', 'Simple code generation', 'Documentation']
  },
  COMPLEX: {
    level: 3,
    description: 'Advanced reasoning, complex code architecture, multi-step analysis',
    examples: ['Complex code architecture', 'Advanced reasoning', 'Critical business logic', 'Multi-step analysis']
  }
};

// Capability requirements for different task types
const TASK_REQUIREMENTS = {
  TEXT_PROCESSING: ['textProcessing'],
  CODE_GENERATION: ['codeGeneration', 'reasoning'],
  ANALYSIS: ['reasoning', 'complexAnalysis'],
  CREATIVE: ['creativity', 'reasoning'],
  VALIDATION: ['reasoning', 'complexAnalysis'],
  MULTI_STEP: ['multiStep', 'reasoning', 'complexAnalysis']
};

module.exports = {
  AI_MODELS,
  TASK_COMPLEXITY,
  TASK_REQUIREMENTS
};
