import React, { useState, useEffect } from 'react';
import { ChevronDown, ChevronRight, CheckSquare, Square, Clock, User, AlertTriangle } from 'lucide-react';
import './TasksList.css';

const TasksList = ({ projectId, storyId }) => {
  const [tasks, setTasks] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [isExpanded, setIsExpanded] = useState(false);

  useEffect(() => {
    if (projectId && storyId) {
      fetchTasks();
    }
  }, [projectId, storyId]);

  const fetchTasks = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await fetch(`/api/projects/${projectId}/stories/${storyId}/tasks`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch tasks');
      }
      
      const tasksData = await response.json();
      setTasks(tasksData);
    } catch (err) {
      console.error('Error fetching tasks:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const getPriorityIcon = (priority) => {
    switch (priority?.toLowerCase()) {
      case 'critical':
        return <AlertTriangle size={14} className="priority-critical" />;
      case 'high':
        return <AlertTriangle size={14} className="priority-high" />;
      case 'medium':
        return <Clock size={14} className="priority-medium" />;
      case 'low':
        return <Clock size={14} className="priority-low" />;
      default:
        return <Clock size={14} className="priority-medium" />;
    }
  };

  const getStatusIcon = (status) => {
    switch (status?.toLowerCase()) {
      case 'done':
      case 'completed':
        return <CheckSquare size={14} className="status-done" />;
      case 'in_progress':
      case 'in-progress':
        return <Clock size={14} className="status-in-progress" />;
      default:
        return <Square size={14} className="status-todo" />;
    }
  };

  const getTypeColor = (type) => {
    switch (type?.toLowerCase()) {
      case 'frontend':
        return '#3b82f6'; // blue
      case 'backend':
        return '#10b981'; // green
      case 'testing':
        return '#f59e0b'; // yellow
      case 'design':
        return '#8b5cf6'; // purple
      case 'documentation':
        return '#6b7280'; // gray
      default:
        return '#6366f1'; // indigo
    }
  };

  if (loading) {
    return (
      <div className="tasks-section">
        <div className="tasks-header">
          <h4>AI Generated Tasks</h4>
          <div className="loading-spinner">Loading...</div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="tasks-section">
        <div className="tasks-header">
          <h4>AI Generated Tasks</h4>
          <span className="error-text">Error: {error}</span>
        </div>
      </div>
    );
  }

  if (tasks.length === 0) {
    return (
      <div className="tasks-section">
        <div className="tasks-header">
          <h4>AI Generated Tasks</h4>
          <span className="no-tasks">No tasks generated yet</span>
        </div>
      </div>
    );
  }

  const totalHours = tasks.reduce((sum, task) => sum + (task.estimated_hours || 0), 0);
  const completedTasks = tasks.filter(task => task.status === 'done' || task.status === 'completed').length;

  return (
    <div className="tasks-section">
      <div className="tasks-header" onClick={() => setIsExpanded(!isExpanded)}>
        <div className="tasks-title">
          {isExpanded ? <ChevronDown size={16} /> : <ChevronRight size={16} />}
          <h4>AI Generated Tasks ({tasks.length})</h4>
        </div>
        <div className="tasks-summary">
          <span className="tasks-progress">{completedTasks}/{tasks.length} completed</span>
          <span className="tasks-hours">{totalHours}h estimated</span>
        </div>
      </div>
      
      {isExpanded && (
        <div className="tasks-list">
          {tasks.map((task) => (
            <div key={task.id} className="task-item">
              <div className="task-header">
                <div className="task-status">
                  {getStatusIcon(task.status)}
                  <span className="task-title">{task.title}</span>
                </div>
                <div className="task-meta">
                  <span 
                    className="task-type" 
                    style={{ backgroundColor: getTypeColor(task.type) }}
                  >
                    {task.type || 'development'}
                  </span>
                  {getPriorityIcon(task.priority)}
                  <span className="task-hours">{task.estimated_hours || 0}h</span>
                </div>
              </div>
              
              {task.description && (
                <div className="task-description">
                  {task.description}
                </div>
              )}
              
              <div className="task-footer">
                <div className="task-assignee">
                  <User size={12} />
                  <span>{task.assigned_to || 'Unassigned'}</span>
                </div>
                <div className="task-dates">
                  <span>Created: {new Date(task.created_at).toLocaleDateString()}</span>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default TasksList;
