import { useState, useEffect } from 'react';
import { CheckCircle, Target, TrendingUp, Clock, AlertCircle, Zap } from 'lucide-react';
import './OutcomeBasedSprintControls.css';

const OutcomeBasedSprintControls = ({ sprint, projectId, onSprintComplete }) => {
  const [completionCheck, setCompletionCheck] = useState(null);
  const [scopeSuggestion, setScopeSuggestion] = useState(null);
  const [loading, setLoading] = useState(false);
  const [checking, setChecking] = useState(false);

  useEffect(() => {
    if (sprint && sprint.status === 'active') {
      checkCompletionCriteria();
    }
    if (sprint && sprint.status === 'planning') {
      getScopeSuggestion();
    }
  }, [sprint, projectId]);

  const checkCompletionCriteria = async () => {
    if (!sprint || !projectId) return;
    
    setChecking(true);
    try {
      const response = await fetch(
        `http://localhost:3000/projects/${projectId}/sprints/${sprint.id}/completion-check`
      );
      
      if (response.ok) {
        const data = await response.json();
        setCompletionCheck(data);
      }
    } catch (error) {
      console.error('Error checking completion criteria:', error);
    } finally {
      setChecking(false);
    }
  };

  const getScopeSuggestion = async () => {
    if (!projectId) return;
    
    try {
      const response = await fetch(
        `http://localhost:3000/projects/${projectId}/sprint-scope-suggestion?targetDays=14`
      );
      
      if (response.ok) {
        const data = await response.json();
        setScopeSuggestion(data);
      }
    } catch (error) {
      console.error('Error getting scope suggestion:', error);
    }
  };

  const handleCompleteSprint = async () => {
    if (!sprint || !projectId || !completionCheck?.canComplete) return;
    
    setLoading(true);
    try {
      const response = await fetch(
        `http://localhost:3000/projects/${projectId}/sprints/${sprint.id}/complete`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            reason: completionCheck.reason
          })
        }
      );
      
      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          onSprintComplete && onSprintComplete(sprint.id);
        }
      }
    } catch (error) {
      console.error('Error completing sprint:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = () => {
    if (!completionCheck) return <Clock className="status-icon neutral" />;
    
    if (completionCheck.canComplete) {
      return <CheckCircle className="status-icon success" />;
    } else {
      return <Target className="status-icon warning" />;
    }
  };

  const getStatusColor = () => {
    if (!completionCheck) return 'neutral';
    return completionCheck.canComplete ? 'success' : 'warning';
  };

  if (!sprint) return null;

  return (
    <div className="outcome-based-controls">
      {/* Active Sprint - Completion Check */}
      {sprint.status === 'active' && (
        <div className="completion-check">
          <div className="check-header">
            <div className="check-title">
              {getStatusIcon()}
              <span>Outcome-Based Completion</span>
            </div>
            <button 
              onClick={checkCompletionCriteria}
              className="refresh-button"
              disabled={checking}
            >
              {checking ? 'Checking...' : 'Refresh'}
            </button>
          </div>

          {completionCheck && (
            <div className={`completion-status ${getStatusColor()}`}>
              <div className="status-message">
                <AlertCircle className="message-icon" />
                <span>{completionCheck.reason}</span>
              </div>

              {completionCheck.metrics && (
                <div className="completion-metrics">
                  <div className="metric">
                    <span className="metric-label">Complexity Delivered</span>
                    <span className="metric-value">{completionCheck.metrics.complexityDelivered}%</span>
                  </div>
                  <div className="metric">
                    <span className="metric-label">Stories Completed</span>
                    <span className="metric-value">{completionCheck.metrics.storiesCompleted}%</span>
                  </div>
                  <div className="metric">
                    <span className="metric-label">Days Running</span>
                    <span className="metric-value">{completionCheck.metrics.daysTaken}</span>
                  </div>
                </div>
              )}

              {completionCheck.canComplete && (
                <button
                  onClick={handleCompleteSprint}
                  className="complete-sprint-button"
                  disabled={loading}
                >
                  {loading ? 'Completing...' : 'Complete Sprint'}
                </button>
              )}
            </div>
          )}
        </div>
      )}

      {/* Planning Sprint - Scope Suggestion */}
      {sprint.status === 'planning' && scopeSuggestion && (
        <div className="scope-suggestion">
          <div className="suggestion-header">
            <TrendingUp className="suggestion-icon" />
            <span>Velocity-Based Scope Suggestion</span>
          </div>

          <div className="suggestion-content">
            <div className="suggestion-metrics">
              <div className="metric">
                <span className="metric-label">Suggested Complexity</span>
                <span className="metric-value">{scopeSuggestion.suggestedComplexity}</span>
              </div>
              <div className="metric">
                <span className="metric-label">Suggested Stories</span>
                <span className="metric-value">{scopeSuggestion.suggestedStories}</span>
              </div>
              {scopeSuggestion.dailyVelocity && (
                <div className="metric">
                  <span className="metric-label">Daily Velocity</span>
                  <span className="metric-value">{scopeSuggestion.dailyVelocity.toFixed(1)}</span>
                </div>
              )}
            </div>

            <div className="suggestion-reason">
              <span className={`confidence ${scopeSuggestion.confidence}`}>
                {scopeSuggestion.confidence.toUpperCase()} CONFIDENCE
              </span>
              <span className="reason-text">{scopeSuggestion.reason}</span>
            </div>
          </div>
        </div>
      )}

      {/* Flow Principles Reminder */}
      <div className="flow-principles-reminder">
        <div className="principle">
          <Zap className="principle-icon" />
          <span>Focus on outcome delivery, not time adherence</span>
        </div>
        <div className="principle">
          <Target className="principle-icon" />
          <span>Complete when value goals are achieved</span>
        </div>
      </div>
    </div>
  );
};

export default OutcomeBasedSprintControls;
