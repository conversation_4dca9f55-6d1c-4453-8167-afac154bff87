const fs = require('fs');
const path = require('path');
const { getAcceptanceCriteriaValidationCompletion } = require('./ai');
const { AICommentManager } = require('./ai-comments');
const db = require('./database');

class AcceptanceCriteriaValidator {
  constructor() {
    this.commentManager = new AICommentManager();
    this.agentName = 'AcceptanceCriteriaValidator';
  }

  /**
   * Validate if implemented code meets the acceptance criteria for a user story
   * @param {Object} userStory - The user story with acceptance criteria
   * @param {string} projectId - The project ID
   * @param {string} workspacePath - Path to the project workspace
   * @returns {Object} Validation result with pass/fail status and feedback
   */
  async validateStoryImplementation(userStory, projectId, workspacePath) {
    try {
      console.log(`🔍 Validating story implementation: ${userStory.title}`);

      // Log validation start
      await this.commentManager.addComment(
        projectId,
        'user_story',
        userStory.id,
        this.agentName,
        `Starting acceptance criteria validation for story: ${userStory.title}`,
        'progress',
        { validation_type: 'acceptance_criteria' }
      );

      // Read implemented code files
      const codeFiles = await this.readImplementedCode(workspacePath, userStory);
      
      if (codeFiles.length === 0) {
        const result = {
          passed: false,
          score: 0,
          feedback: 'No implemented code files found for this story.',
          details: 'The story appears to be marked as implemented but no code files were found.',
          codeFiles: []
        };

        await this.logValidationResult(projectId, userStory.id, result);
        return result;
      }

      // Perform AI-based validation
      const validationResult = await this.performAIValidation(userStory, codeFiles);

      // Log validation result and update database
      await this.logValidationResult(projectId, userStory.id, validationResult);
      await this.updateValidationInDatabase(userStory.id, validationResult);

      return validationResult;

    } catch (error) {
      console.error(`❌ Error validating story ${userStory.title}:`, error);
      
      const errorResult = {
        passed: false,
        score: 0,
        feedback: `Validation failed due to error: ${error.message}`,
        details: error.stack,
        codeFiles: []
      };

      await this.commentManager.addComment(
        projectId,
        'user_story',
        userStory.id,
        this.agentName,
        `Validation failed with error: ${error.message}`,
        'blocker',
        { error: error.message, validation_type: 'acceptance_criteria' },
        { priority: 'high' }
      );

      return errorResult;
    }
  }

  /**
   * Read implemented code files related to the user story
   * @param {string} workspacePath - Path to the project workspace
   * @param {Object} userStory - The user story
   * @returns {Array} Array of code file objects with path and content
   */
  async readImplementedCode(workspacePath, userStory) {
    const codeFiles = [];
    
    try {
      if (!workspacePath || !fs.existsSync(workspacePath)) {
        console.log(`⚠️ Workspace path not found: ${workspacePath}`);
        return codeFiles;
      }

      // Look for files that might be related to this story
      // This is a simplified approach - in a real system you'd want more sophisticated file tracking
      const fileExtensions = ['.js', '.jsx', '.ts', '.tsx', '.py', '.java', '.cs', '.php', '.rb', '.go'];
      
      await this.scanDirectory(workspacePath, codeFiles, fileExtensions, userStory);
      
      console.log(`📁 Found ${codeFiles.length} code files for validation`);
      return codeFiles;

    } catch (error) {
      console.error('Error reading implemented code:', error);
      return codeFiles;
    }
  }

  /**
   * Recursively scan directory for code files
   * @param {string} dirPath - Directory path to scan
   * @param {Array} codeFiles - Array to store found files
   * @param {Array} extensions - File extensions to look for
   * @param {Object} userStory - The user story for context
   */
  async scanDirectory(dirPath, codeFiles, extensions, userStory, depth = 0) {
    if (depth > 3) return; // Limit recursion depth

    try {
      const items = fs.readdirSync(dirPath);
      
      for (const item of items) {
        const itemPath = path.join(dirPath, item);
        const stat = fs.statSync(itemPath);
        
        if (stat.isDirectory()) {
          // Skip common directories that don't contain implementation code
          if (!['node_modules', '.git', 'dist', 'build', '.next', 'coverage'].includes(item)) {
            await this.scanDirectory(itemPath, codeFiles, extensions, userStory, depth + 1);
          }
        } else if (stat.isFile()) {
          const ext = path.extname(item);
          if (extensions.includes(ext)) {
            try {
              const content = fs.readFileSync(itemPath, 'utf8');
              // Only include files that seem relevant (contain keywords from story or are recently modified)
              if (this.isFileRelevant(content, userStory, stat.mtime)) {
                codeFiles.push({
                  path: path.relative(dirPath, itemPath),
                  content: content,
                  size: stat.size,
                  modified: stat.mtime
                });
              }
            } catch (readError) {
              console.warn(`Could not read file ${itemPath}:`, readError.message);
            }
          }
        }
      }
    } catch (error) {
      console.warn(`Could not scan directory ${dirPath}:`, error.message);
    }
  }

  /**
   * Check if a file is relevant to the user story
   * @param {string} content - File content
   * @param {Object} userStory - The user story
   * @param {Date} modifiedTime - File modification time
   * @returns {boolean} True if file seems relevant
   */
  isFileRelevant(content, userStory, modifiedTime) {
    // Check if file was modified recently (within last 30 days)
    const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    const isRecentlyModified = modifiedTime > thirtyDaysAgo;
    
    // Extract keywords from story title and description
    const storyText = `${userStory.title} ${userStory.description}`.toLowerCase();
    const keywords = storyText.match(/\b\w{3,}\b/g) || [];
    
    // Check if content contains story-related keywords
    const contentLower = content.toLowerCase();
    const hasRelevantKeywords = keywords.some(keyword => 
      contentLower.includes(keyword) && keyword.length > 3
    );
    
    return isRecentlyModified || hasRelevantKeywords;
  }

  /**
   * Perform AI-based validation of code against acceptance criteria
   * @param {Object} userStory - The user story with acceptance criteria
   * @param {Array} codeFiles - Array of code files to validate
   * @returns {Object} Validation result
   */
  async performAIValidation(userStory, codeFiles) {
    const prompt = this.buildValidationPrompt(userStory, codeFiles);

    try {
      // Use intelligent completion for acceptance criteria validation
      const context = {
        userStory,
        codeFiles: codeFiles.map(f => ({ path: f.path, size: f.content.length })),
        totalCodeSize: codeFiles.reduce((sum, f) => sum + f.content.length, 0),
        fileCount: codeFiles.length,
        complexity: userStory.story_points || 3
      };

      const result = await getAcceptanceCriteriaValidationCompletion(prompt, context);
      const validationResult = this.parseValidationResponse(result.content);

      // Add model selection metadata to validation result
      if (result.metadata) {
        validationResult.modelInfo = {
          model: result.metadata.selectedModel,
          modelName: result.metadata.modelName,
          estimatedCost: result.metadata.estimatedCost,
          reasoning: result.metadata.reasoning,
          taskComplexity: result.metadata.taskAnalysis?.complexity
        };
      }

      return validationResult;
    } catch (error) {
      console.error('AI validation failed:', error);
      return {
        passed: false,
        score: 0,
        feedback: `AI validation failed: ${error.message}`,
        details: 'Could not complete AI-based validation',
        codeFiles: codeFiles.map(f => f.path),
        modelInfo: {
          error: error.message,
          fallback: true
        }
      };
    }
  }

  /**
   * Build the prompt for AI validation
   * @param {Object} userStory - The user story
   * @param {Array} codeFiles - Code files to validate
   * @returns {string} The validation prompt
   */
  buildValidationPrompt(userStory, codeFiles) {
    const filesContent = codeFiles.map(file => 
      `File: ${file.path}\n\`\`\`\n${file.content}\n\`\`\``
    ).join('\n\n');

    return `You are a senior software engineer reviewing code implementation against acceptance criteria.

USER STORY:
Title: ${userStory.title}
Description: ${userStory.description}

ACCEPTANCE CRITERIA:
${userStory.acceptance_criteria}

IMPLEMENTED CODE:
${filesContent}

Please evaluate if the implemented code meets the acceptance criteria. Provide your response in the following JSON format:

{
  "passed": true/false,
  "score": 0-100,
  "feedback": "Brief summary of validation result",
  "details": "Detailed explanation of what passes/fails and why",
  "recommendations": "Specific recommendations for improvement if needed"
}

Focus on:
1. Does the code implement the required functionality?
2. Are all acceptance criteria addressed?
3. Is the implementation complete and functional?
4. Are there any obvious bugs or issues?
5. Does the code follow good practices?

Be thorough but concise in your evaluation.`;
  }

  /**
   * Parse the AI validation response
   * @param {string} aiResponse - Raw AI response
   * @returns {Object} Parsed validation result
   */
  parseValidationResponse(aiResponse) {
    try {
      // Try to extract JSON from the response using improved parsing
      let jsonString = null;

      // Method 1: Look for JSON between code blocks
      const codeBlockMatch = aiResponse.match(/```(?:json)?\s*(\{[\s\S]*?\})\s*```/);
      if (codeBlockMatch) {
        jsonString = codeBlockMatch[1];
      } else {
        // Method 2: Find the first complete JSON object
        const startIndex = aiResponse.indexOf('{');
        if (startIndex !== -1) {
          let braceCount = 0;
          let endIndex = startIndex;

          for (let i = startIndex; i < aiResponse.length; i++) {
            if (aiResponse[i] === '{') braceCount++;
            if (aiResponse[i] === '}') braceCount--;
            if (braceCount === 0) {
              endIndex = i;
              break;
            }
          }

          if (braceCount === 0) {
            jsonString = aiResponse.substring(startIndex, endIndex + 1);
          }
        }
      }

      if (jsonString) {
        const parsed = JSON.parse(jsonString);
        return {
          passed: Boolean(parsed.passed),
          score: Math.max(0, Math.min(100, parseInt(parsed.score) || 0)),
          feedback: parsed.feedback || 'No feedback provided',
          details: parsed.details || 'No details provided',
          recommendations: parsed.recommendations || '',
          rawResponse: aiResponse
        };
      }
    } catch (error) {
      console.error('Error parsing AI validation response:', error);
      console.error('Response preview:', aiResponse.substring(0, 500) + '...');
    }

    // Fallback parsing if JSON extraction fails
    const passed = aiResponse.toLowerCase().includes('passed: true') ||
                   aiResponse.toLowerCase().includes('"passed": true');

    return {
      passed: passed,
      score: passed ? 75 : 25,
      feedback: 'AI validation completed but response format was unexpected',
      details: aiResponse,
      recommendations: '',
      rawResponse: aiResponse
    };
  }

  /**
   * Log validation result as a comment
   * @param {string} projectId - Project ID
   * @param {string} storyId - Story ID
   * @param {Object} result - Validation result
   */
  async logValidationResult(projectId, storyId, result) {
    const status = result.passed ? 'success' : 'warning';
    const message = `Acceptance criteria validation ${result.passed ? 'PASSED' : 'FAILED'} (Score: ${result.score}/100)\n\n${result.feedback}`;

    await this.commentManager.addComment(
      projectId,
      'user_story',
      storyId,
      this.agentName,
      message,
      status,
      {
        validation_type: 'acceptance_criteria',
        passed: result.passed,
        score: result.score,
        details: result.details,
        recommendations: result.recommendations
      },
      { priority: result.passed ? 'normal' : 'high' }
    );
  }

  /**
   * Update validation data in the database
   * @param {string} storyId - Story ID
   * @param {Object} result - Validation result
   */
  async updateValidationInDatabase(storyId, result) {
    return new Promise((resolve, reject) => {
      const query = `UPDATE user_stories
                     SET validation_score = ?,
                         validation_feedback = ?,
                         updated_at = CURRENT_TIMESTAMP
                     WHERE id = ?`;

      const feedback = JSON.stringify({
        passed: result.passed,
        feedback: result.feedback,
        details: result.details,
        recommendations: result.recommendations,
        validated_at: new Date().toISOString()
      });

      db.run(query, [result.score, feedback, storyId], function(err) {
        if (err) {
          console.error('Error updating validation data:', err);
          reject(err);
        } else {
          resolve(this.changes);
        }
      });
    });
  }
}

module.exports = { AcceptanceCriteriaValidator };
