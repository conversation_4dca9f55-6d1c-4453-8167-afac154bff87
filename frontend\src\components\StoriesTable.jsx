import React, { useMemo, useState } from 'react';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import {
  useSortable,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import {
  MoreHorizontal,
  ArrowUp,
  ArrowDown,
  Minus,
  AlertTriangle,
  CheckCircle,
  Clock,
  GitBranch
} from 'lucide-react';
import UserStoryModal from './UserStoryModal';
import StoryDetailsModal from './StoryDetailsModal';

import './StoriesTable.css';

const SortableStoryRow = ({ story, onSelect, isSelected, onStatusChange, onPriorityChange, onDoubleClick, dragDisabled }) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: story.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  const getPriorityIcon = (priority) => {
    switch (priority) {
      case 'critical':
        return <ArrowUp className="priority-icon critical" size={14} />;
      case 'high':
        return <ArrowUp className="priority-icon high" size={14} />;
      case 'medium':
        return <Minus className="priority-icon medium" size={14} />;
      case 'low':
        return <ArrowDown className="priority-icon low" size={14} />;
      default:
        return <Minus className="priority-icon medium" size={14} />;
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'done':
        return <CheckCircle className="status-icon done" size={14} />;
      case 'in_progress':
        return <Clock className="status-icon in-progress" size={14} />;
      case 'backlog':
        return <Clock className="status-icon backlog" size={14} />;
      default:
        return <Clock className="status-icon backlog" size={14} />;
    }
  };

  const getImplementationStatusBadge = (implementationStatus, version) => {
    switch (implementationStatus) {
      case 'implemented':
        return (
          <span className="implementation-badge implemented">
            <CheckCircle size={12} />
            Implemented
          </span>
        );
      case 'modified_after_implementation':
        return (
          <span className="implementation-badge modified">
            <AlertTriangle size={12} />
            Modified
          </span>
        );
      default:
        return (
          <span className="implementation-badge not-started">
            <Clock size={12} />
            Not Started
          </span>
        );
    }
  };

  return (
    <tr
      ref={setNodeRef}
      style={style}
      className={`story-row ${isSelected ? 'selected' : ''} ${isDragging ? 'dragging' : ''}`}
      onClick={() => onSelect(story)}
      onDoubleClick={() => onDoubleClick && onDoubleClick(story)}
      {...attributes}
    >
      <td className="drag-handle" {...(dragDisabled ? {} : listeners)}>
        <div className="drag-indicator">⋮⋮</div>
      </td>
      <td className="story-id">
        <span className="id-badge">#{story.story_number || story.id}</span>
      </td>
      <td className="story-title">
        <div className="title-content">
          <span className="title-text">{story.title}</span>
        </div>
      </td>
      <td className="story-epic">
        {story.epic && <span className="epic-badge">{story.epic}</span>}
      </td>
      <td className="story-status">
        <div className="status-dropdown">
          {getStatusIcon(story.status)}
          <select
            value={story.status}
            onChange={(e) => onStatusChange(story.id, e.target.value)}
            onClick={(e) => e.stopPropagation()}
          >
            <option value="backlog">Backlog</option>
            <option value="ready">Ready</option>
            <option value="in_progress">In Progress</option>
            <option value="testing">Testing</option>
            <option value="done">Done</option>
          </select>
        </div>
      </td>
      <td className="story-priority">
        <div className="priority-dropdown">
          {getPriorityIcon(story.priority)}
          <select
            value={story.priority}
            onChange={(e) => onPriorityChange(story.id, e.target.value)}
            onClick={(e) => e.stopPropagation()}
          >
            <option value="low">Low</option>
            <option value="medium">Medium</option>
            <option value="high">High</option>
            <option value="critical">Critical</option>
          </select>
        </div>
      </td>
      <td className="story-points-cell">
        <span className="points-badge">{story.story_points}</span>
      </td>
      <td className="story-implementation">
        {getImplementationStatusBadge(story.implementation_status, story.implemented_in_version)}
      </td>
      <td className="story-version">
        {story.implemented_in_version && (
          <span className="version-badge">
            <GitBranch size={12} />
            v{story.implemented_in_version}
          </span>
        )}
      </td>
      <td className="story-actions">
        <button className="action-btn" onClick={(e) => e.stopPropagation()}>
          <MoreHorizontal size={16} />
        </button>
      </td>
    </tr>
  );
};

const StoriesTable = ({ stories, onStoriesReorder, onStorySelect, selectedStory, onStoryUpdate, projectId }) => {
  // Base list that reflects user's manual order (drag & drop)
  const [baseStories, setBaseStories] = useState(stories);
  // View list after applying search and sort
  const [viewStories, setViewStories] = useState(stories);

  const [search, setSearch] = useState('');
  const [sortState, setSortState] = useState({ key: null, direction: null }); // direction: 'asc' | 'desc' | null
  const [epicFilter, setEpicFilter] = useState(''); // New epic filter state
  const [showCreateModal, setShowCreateModal] = useState(false);

  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [detailsStory, setDetailsStory] = useState(null);

  const dragDisabled = Boolean(sortState.direction) || search.trim().length > 0;

  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  const handleDragEnd = (event) => {
    if (dragDisabled) return;
    const { active, over } = event;
    if (!over || active.id === over.id) return;

    const oldIndex = baseStories.findIndex(story => story.id === active.id);
    const newIndex = baseStories.findIndex(story => story.id === over.id);
    if (oldIndex === -1 || newIndex === -1) return;

    const newBase = arrayMove(baseStories, oldIndex, newIndex);
    setBaseStories(newBase);
    onStoriesReorder(newBase);
  };

  const handleStatusChange = (storyId, newStatus) => {
    onStoryUpdate(storyId, { status: newStatus });
  };

  const handlePriorityChange = (storyId, newPriority) => {
    onStoryUpdate(storyId, { priority: newPriority });
  };

  const handleStoryDoubleClick = (story) => {
    // TODO: Re-implement detail modal functionality
    console.log('Double-clicked story:', story);
    setDetailsStory(story);
    setShowDetailsModal(true);

  };

  // Recompute view when inputs change
  const priorityOrder = { low: 0, medium: 1, high: 2, critical: 3 };
  const statusOrder = { backlog: 0, in_progress: 1, done: 2 };
  const implOrder = { not_started: 0, modified_after_implementation: 1, implemented: 2 };

  const applyView = React.useCallback(() => {
    const q = search.trim().toLowerCase();
    let list = [...baseStories];

    // Apply search filter
    if (q) {
      list = list.filter((s) =>
        (s.title || '').toLowerCase().includes(q) ||
        (s.id || '').toLowerCase().includes(q) ||
        (s.epic || '').toLowerCase().includes(q)
      );
    }

    // Apply epic filter
    if (epicFilter) {
      list = list.filter((s) => s.epic === epicFilter);
    }

    if (sortState.direction && sortState.key) {
      list.sort((a, b) => {
        let cmp = 0;
        switch (sortState.key) {
          case 'id':
            cmp = (a.id || '').localeCompare(b.id || '');
            break;
          case 'title':
            cmp = (a.title || '').localeCompare(b.title || '');
            break;
          case 'status':
            cmp = (statusOrder[a.status] ?? 0) - (statusOrder[b.status] ?? 0);
            break;
          case 'priority':
            cmp = (priorityOrder[a.priority] ?? 0) - (priorityOrder[b.priority] ?? 0);
            break;
          case 'points':
            cmp = (a.story_points ?? 0) - (b.story_points ?? 0);
            break;
          case 'implementation':
            cmp = (implOrder[a.implementation_status] ?? 0) - (implOrder[b.implementation_status] ?? 0);
            break;
          case 'version':
            cmp = (a.implemented_in_version || '').localeCompare(b.implemented_in_version || '');
            break;
          case 'epic':
            cmp = (a.epic || '').localeCompare(b.epic || '');
            break;
          default:
            cmp = 0;
        }
        return sortState.direction === 'asc' ? cmp : -cmp;
      });
    }

    setViewStories(list);
  }, [baseStories, search, sortState, epicFilter]);

  React.useEffect(() => {
    setBaseStories(stories);
  }, [stories]);

  React.useEffect(() => {
    applyView();
  }, [applyView]);

  // Get unique epics for filter dropdown
  const getUniqueEpics = () => {
    const epics = baseStories
      .map(story => story.epic)
      .filter(epic => epic && epic.trim() !== '')
      .filter((epic, index, arr) => arr.indexOf(epic) === index)
      .sort();
    return epics;
  };

  const handleHeaderSort = (key) => {
    setSortState((prev) => {
      if (prev.key !== key) return { key, direction: 'asc' };
      if (prev.direction === 'asc') return { key, direction: 'desc' };
      if (prev.direction === 'desc') return { key: null, direction: null };
      return { key, direction: 'asc' };
    });
  };

  const handleCreateStory = async (storyData) => {
    try {
      const response = await fetch(`http://localhost:3000/projects/${projectId}/stories`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(storyData)
      });

      if (response.ok) {
        const newStory = await response.json();
        // Refresh the stories list by calling the parent's update function
        if (onStoryUpdate) {
          onStoryUpdate();
        }
        setShowCreateModal(false);
      }
    } catch (error) {
      console.error('Error creating story:', error);
    }
  };

  return (
    <div className="stories-table-container">
      <div className="table-header">
        <div className="header-title">
          <h2>User Stories</h2>
        </div>
        <div className="table-actions">
          <select
            className="epic-filter"
            value={epicFilter}
            onChange={(e) => setEpicFilter(e.target.value)}
          >
            <option value="">All Epics</option>
            {getUniqueEpics().map(epic => (
              <option key={epic} value={epic}>{epic}</option>
            ))}
          </select>
          <input
            className="search-input"
            type="search"
            placeholder="Search stories…"
            value={search}
            onChange={(e) => setSearch(e.target.value)}
          />
          <button
            className="btn-primary btn-compact"
            onClick={() => setShowCreateModal(true)}
          >
            New Story
          </button>
        </div>
      </div>

      <DndContext
        sensors={sensors}
        collisionDetection={closestCenter}
        onDragEnd={handleDragEnd}
      >
        <div className="table-wrapper">
          {stories.length === 0 ? (
            <div className="empty-state">
              <div className="empty-icon">📝</div>
              <h3>No stories yet</h3>
              <p>Create your first user story to get started</p>
              <button
                className="btn-primary"
                onClick={() => setShowCreateModal(true)}
              >
                Create Story
              </button>
            </div>
          ) : stories.length > 0 && viewStories.length === 0 ? (
            <div className="empty-state">
              <div className="empty-icon">🔍</div>
              <h3>No stories match your filters</h3>
              <p>Try adjusting your search or epic filter</p>
            </div>
          ) : (
            <table className="stories-table">
            <thead>
              <tr>
                <th className="drag-column"></th>
                <th className="id-column sortable" onClick={() => handleHeaderSort('id')}>
                  <span className="th-content">ID {sortState.key === 'id' && (sortState.direction === 'asc' ? <ArrowUp size={12}/> : <ArrowDown size={12}/> )}</span>
                </th>
                <th className="title-column sortable" onClick={() => handleHeaderSort('title')}>
                  <span className="th-content">Title {sortState.key === 'title' && (sortState.direction === 'asc' ? <ArrowUp size={12}/> : <ArrowDown size={12}/> )}</span>
                </th>
                <th className="epic-column sortable" onClick={() => handleHeaderSort('epic')}>
                  <span className="th-content">Epic {sortState.key === 'epic' && (sortState.direction === 'asc' ? <ArrowUp size={12}/> : <ArrowDown size={12}/> )}</span>
                </th>
                <th className="status-column sortable" onClick={() => handleHeaderSort('status')}>
                  <span className="th-content">Status {sortState.key === 'status' && (sortState.direction === 'asc' ? <ArrowUp size={12}/> : <ArrowDown size={12}/> )}</span>
                </th>
                <th className="priority-column sortable" onClick={() => handleHeaderSort('priority')}>
                  <span className="th-content">Priority {sortState.key === 'priority' && (sortState.direction === 'asc' ? <ArrowUp size={12}/> : <ArrowDown size={12}/> )}</span>
                </th>
                <th className="points-column sortable" onClick={() => handleHeaderSort('points')}>
                  <span className="th-content">Points {sortState.key === 'points' && (sortState.direction === 'asc' ? <ArrowUp size={12}/> : <ArrowDown size={12}/> )}</span>
                </th>
                <th className="implementation-column sortable" onClick={() => handleHeaderSort('implementation')}>
                  <span className="th-content">Implementation {sortState.key === 'implementation' && (sortState.direction === 'asc' ? <ArrowUp size={12}/> : <ArrowDown size={12}/> )}</span>
                </th>
                <th className="version-column sortable" onClick={() => handleHeaderSort('version')}>
                  <span className="th-content">Version {sortState.key === 'version' && (sortState.direction === 'asc' ? <ArrowUp size={12}/> : <ArrowDown size={12}/> )}</span>
                </th>
                <th className="actions-column"></th>
              </tr>
            </thead>
            <tbody>
              <SortableContext
                items={viewStories.map(story => story.id)}
                strategy={verticalListSortingStrategy}
              >
                {viewStories.map((story) => (
                  <SortableStoryRow
                    key={story.id}
                    story={story}
                    onSelect={onStorySelect}
                    isSelected={selectedStory?.id === story.id}
                    onStatusChange={handleStatusChange}
                    onPriorityChange={handlePriorityChange}
                    onDoubleClick={handleStoryDoubleClick}
                    dragDisabled={dragDisabled}
                  />
                ))}
              </SortableContext>
            </tbody>
          </table>
          )}
        </div>
      </DndContext>

      <UserStoryModal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        onSave={handleCreateStory}
        projectId={projectId}
      />

      <StoryDetailsModal
        isOpen={showDetailsModal}
        onClose={() => setShowDetailsModal(false)}
        story={detailsStory}
        projectId={projectId}
      />



    </div>
  );
};

export default StoriesTable;
