.ai-generator-modal .slideout-content {
  padding: 0;
}

.ai-generator-content {
  padding: 24px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* Step Header */
.step-header {
  margin-bottom: 24px;
}

.step-header h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #ffffff;
}

.step-header p {
  margin: 0;
  color: #9ca3af;
  font-size: 14px;
  line-height: 1.5;
}

/* Input Step */
.generator-input-step {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.form-group {
  margin-bottom: 24px;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #ffffff;
  font-size: 14px;
}

.form-group textarea {
  flex: 1;
  min-height: 120px;
  padding: 12px;
  border: 1px solid #374151;
  border-radius: 6px;
  font-size: 14px;
  font-family: inherit;
  resize: vertical;
  transition: border-color 0.2s ease;
  background: #1a1a1a;
  color: #ffffff;
}

.form-group textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-group textarea.error {
  border-color: #ef4444;
}

.form-group textarea:disabled {
  background-color: #2d2d2d;
  cursor: not-allowed;
}

.input-hint {
  margin-top: 8px;
  font-size: 12px;
  color: #6b7280;
}

.error-message {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-top: 8px;
  color: #ef4444;
  font-size: 13px;
}

/* Loading Step */
.generator-loading-step {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-content {
  text-align: center;
  max-width: 300px;
}

.loading-content .spinner {
  color: #3b82f6;
  animation: spin 1s linear infinite;
  margin-bottom: 24px;
}

.loading-content h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #ffffff;
}

.loading-content p {
  margin: 0 0 24px 0;
  color: #9ca3af;
  font-size: 14px;
}

.loading-steps {
  display: flex;
  flex-direction: column;
  gap: 12px;
  text-align: left;
}

.loading-step {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 13px;
  color: #6b7280;
}

.loading-step.active {
  color: #ffffff;
}

.loading-step .spinner-small {
  animation: spin 1s linear infinite;
}

.loading-step .step-dot {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background-color: #374151;
}

/* Preview Step */
.generator-preview-step {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.story-preview {
  flex: 1;
  background: #2d2d2d;
  border: 1px solid #374151;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 24px;
  overflow-y: auto;
}

.preview-field {
  margin-bottom: 20px;
}

.preview-field:last-child {
  margin-bottom: 0;
}

.preview-field label {
  display: block;
  margin-bottom: 6px;
  font-weight: 600;
  color: #ffffff;
  font-size: 13px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.preview-value {
  color: #ffffff;
  font-size: 14px;
  line-height: 1.5;
}

.preview-value.acceptance-criteria {
  white-space: pre-line;
  background: #1a1a1a;
  padding: 12px;
  border-radius: 4px;
  border: 1px solid #374151;
}

.preview-metadata {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  margin: 20px 0;
  padding: 16px;
  background: #1a1a1a;
  border-radius: 6px;
  border: 1px solid #374151;
}

.metadata-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 13px;
}

.metadata-item .label {
  color: #9ca3af;
  font-weight: 500;
}

.metadata-item .value {
  color: #ffffff;
  font-weight: 600;
}

.priority-badge {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.priority-badge.low {
  background: rgba(34, 197, 94, 0.2);
  color: #22c55e;
}

.priority-badge.medium {
  background: rgba(251, 191, 36, 0.2);
  color: #fbbf24;
}

.priority-badge.high {
  background: rgba(249, 115, 22, 0.2);
  color: #f97316;
}

.priority-badge.critical {
  background: rgba(239, 68, 68, 0.2);
  color: #ef4444;
}

/* Actions */
.generator-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 20px;
  border-top: 1px solid #374151;
  margin-top: auto;
}

.generator-actions button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
}

.generator-actions .btn-secondary {
  background: #2d2d2d;
  color: #ffffff;
  border: 1px solid #374151;
}

.generator-actions .btn-secondary:hover:not(:disabled) {
  background: #374151;
}

.generator-actions .btn-primary {
  background: #3b82f6;
  color: white;
}

.generator-actions .btn-primary:hover:not(:disabled) {
  background: #2563eb;
}

.generator-actions button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Animations */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Responsive */
@media (max-width: 768px) {
  .ai-generator-content {
    padding: 16px;
  }
  
  .preview-metadata {
    grid-template-columns: 1fr;
  }
  
  .generator-actions {
    flex-direction: column-reverse;
  }
  
  .generator-actions button {
    width: 100%;
    justify-content: center;
  }
}
