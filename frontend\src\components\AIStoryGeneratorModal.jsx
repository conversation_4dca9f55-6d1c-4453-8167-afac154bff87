import { useState } from 'react';
import { <PERSON><PERSON>les, Loader2, FileText, AlertCircle, CheckCircle } from 'lucide-react';
import SlideoutPanel from './SlideoutPanel';
import './AIStoryGeneratorModal.css';

const AIStoryGeneratorModal = ({ isOpen, onClose, onStoryGenerated, projectId }) => {
  const [description, setDescription] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedStory, setGeneratedStory] = useState(null);
  const [error, setError] = useState('');
  const [step, setStep] = useState('input'); // 'input', 'generating', 'preview'

  const handleReset = () => {
    setDescription('');
    setGeneratedStory(null);
    setError('');
    setStep('input');
    setIsGenerating(false);
  };

  const handleClose = () => {
    handleReset();
    onClose();
  };

  const handleGenerate = async () => {
    if (!description.trim()) {
      setError('Please enter a description for your user story');
      return;
    }

    setIsGenerating(true);
    setError('');
    setStep('generating');

    try {
      const response = await fetch(`/api/projects/${projectId}/stories/generate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ description: description.trim() }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `Server error: ${response.status}`);
      }

      const data = await response.json();
      setGeneratedStory(data.story);
      setStep('preview');
    } catch (err) {
      console.error('Error generating story:', err);
      setError(err.message || 'Failed to generate story. Please try again.');
      setStep('input');
    } finally {
      setIsGenerating(false);
    }
  };

  const handleUseStory = () => {
    if (generatedStory) {
      onStoryGenerated(generatedStory);
      handleClose();
    }
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && e.ctrlKey && !isGenerating && description.trim()) {
      handleGenerate();
    }
  };

  return (
    <SlideoutPanel
      isOpen={isOpen}
      onClose={handleClose}
      title="AI Story Generator"
      icon={Sparkles}
      width="700px"
      className="ai-generator-modal"
    >
      <div className="ai-generator-content">
        {step === 'input' && (
          <div className="generator-input-step">
            <div className="step-header">
              <h3>Describe Your User Story</h3>
              <p>Enter a brief description of what you want to build, and AI will generate a complete user story for you.</p>
            </div>

            <div className="form-group">
              <label htmlFor="description">Story Description</label>
              <textarea
                id="description"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                onKeyDown={handleKeyPress}
                placeholder="e.g., Users should be able to upload and manage their profile pictures"
                rows={4}
                className={error ? 'error' : ''}
                disabled={isGenerating}
              />
              {error && (
                <div className="error-message">
                  <AlertCircle size={16} />
                  {error}
                </div>
              )}
              <div className="input-hint">
                Press Ctrl+Enter to generate, or click the button below
              </div>
            </div>

            <div className="generator-actions">
              <button
                type="button"
                onClick={handleClose}
                className="btn-secondary"
                disabled={isGenerating}
              >
                Cancel
              </button>
              <button
                type="button"
                onClick={handleGenerate}
                className="btn-primary"
                disabled={isGenerating || !description.trim()}
              >
                <Sparkles size={16} />
                Generate Story
              </button>
            </div>
          </div>
        )}

        {step === 'generating' && (
          <div className="generator-loading-step">
            <div className="loading-content">
              <Loader2 size={48} className="spinner" />
              <h3>Generating Your Story...</h3>
              <p>AI is analyzing your description and creating a comprehensive user story.</p>
              <div className="loading-steps">
                <div className="loading-step active">
                  <CheckCircle size={16} />
                  Analyzing description
                </div>
                <div className="loading-step active">
                  <Loader2 size={16} className="spinner-small" />
                  Generating story structure
                </div>
                <div className="loading-step">
                  <div className="step-dot"></div>
                  Creating acceptance criteria
                </div>
              </div>
            </div>
          </div>
        )}

        {step === 'preview' && generatedStory && (
          <div className="generator-preview-step">
            <div className="step-header">
              <h3>Generated User Story</h3>
              <p>Review the AI-generated story below. You can use it as-is or edit it further.</p>
            </div>

            <div className="story-preview">
              <div className="preview-field">
                <label>Title</label>
                <div className="preview-value">{generatedStory.title}</div>
              </div>

              <div className="preview-field">
                <label>Description</label>
                <div className="preview-value">{generatedStory.description}</div>
              </div>

              <div className="preview-field">
                <label>Acceptance Criteria</label>
                <div className="preview-value acceptance-criteria">
                  {generatedStory.acceptance_criteria}
                </div>
              </div>

              <div className="preview-metadata">
                <div className="metadata-item">
                  <span className="label">Priority:</span>
                  <span className={`priority-badge ${generatedStory.priority}`}>
                    {generatedStory.priority}
                  </span>
                </div>
                <div className="metadata-item">
                  <span className="label">Story Points:</span>
                  <span className="value">{generatedStory.story_points}</span>
                </div>
                <div className="metadata-item">
                  <span className="label">Epic:</span>
                  <span className="value">{generatedStory.epic}</span>
                </div>
                <div className="metadata-item">
                  <span className="label">User Persona:</span>
                  <span className="value">{generatedStory.user_persona}</span>
                </div>
              </div>

              {generatedStory.business_value && (
                <div className="preview-field">
                  <label>Business Value</label>
                  <div className="preview-value">{generatedStory.business_value}</div>
                </div>
              )}
            </div>

            <div className="generator-actions">
              <button
                type="button"
                onClick={handleReset}
                className="btn-secondary"
              >
                Generate Another
              </button>
              <button
                type="button"
                onClick={handleUseStory}
                className="btn-primary"
              >
                <FileText size={16} />
                Use This Story
              </button>
            </div>
          </div>
        )}
      </div>
    </SlideoutPanel>
  );
};

export default AIStoryGeneratorModal;
