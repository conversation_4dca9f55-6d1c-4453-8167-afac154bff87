import React, { useEffect } from 'react';
import { X } from 'lucide-react';
import './SlideoutPanel.css';

const SlideoutPanel = ({ 
  isOpen, 
  onClose, 
  title, 
  icon: Icon,
  children,
  width = '600px',
  className = '',
  showCloseButton = true,
  onBackdropClick = true
}) => {
  // Handle escape key
  useEffect(() => {
    const handleEscape = (e) => {
      if (e.key === 'Escape' && isOpen) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      // Prevent body scroll when panel is open
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  const handleBackdropClick = (e) => {
    if (onBackdropClick && e.target === e.currentTarget) {
      onClose();
    }
  };

  return (
    <div className="slideout-overlay" onClick={handleBackdropClick}>
      <div 
        className={`slideout-panel ${className}`}
        style={{ width }}
        onClick={e => e.stopPropagation()}
      >
        {/* Panel Header */}
        <div className="slideout-header">
          <div className="slideout-title">
            {Icon && <Icon size={20} />}
            <h2>{title}</h2>
          </div>
          {showCloseButton && (
            <button 
              className="slideout-close-button"
              onClick={onClose}
              aria-label="Close panel"
            >
              <X size={20} />
            </button>
          )}
        </div>

        {/* Panel Content */}
        <div className="slideout-content">
          {children}
        </div>
      </div>
    </div>
  );
};

export default SlideoutPanel;
