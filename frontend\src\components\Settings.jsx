import React, { useState, useEffect } from 'react';
import {
  Settings as SettingsIcon,
  Brain,
  DollarSign,
  Bot,
  Zap,
  TrendingUp
} from 'lucide-react';
import ConfirmationModal from './ConfirmationModal';
import './Settings.css';

const Settings = () => {
  // AI Model Manager State
  const [models, setModels] = useState({});
  const [costStats, setCostStats] = useState(null);
  const [modelConfig, setModelConfig] = useState({
    costOptimization: 'balanced',
    maxCostPerTask: 0.10,
    qualityThreshold: 7
  });

  // AI Scheduler State
  const [schedulerConfig, setSchedulerConfig] = useState({
    tickRate: 30000,
    maxConcurrentProjects: 3,
    workingHours: {
      enabled: false,
      start: '09:00',
      end: '17:00'
    },
    guardrails: {
      requireActiveProject: true,
      requireActiveSprint: true,
      maxStoriesPerTick: 2,
      cooldownBetweenStories: 5000,
      minSprintDaysRemaining: 1,
      maxConcurrentStoriesPerProject: 3,
      priorityThreshold: 'medium',
      blockOnCriticalIssues: true,
      requireMinimumSprintCapacity: 0.2
    }
  });

  // UI State
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('ai-models');
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [showErrorModal, setShowErrorModal] = useState(false);
  const [modalMessage, setModalMessage] = useState('');

  useEffect(() => {
    fetchAllData();
  }, []);

  const fetchAllData = async () => {
    try {
      setLoading(true);
      await Promise.all([
        fetchModelsData(),
        fetchSchedulerConfig()
      ]);
    } catch (error) {
      console.error('Error fetching settings data:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchModelsData = async () => {
    try {
      const [modelsRes, costsRes] = await Promise.all([
        fetch('/api/ai/models'),
        fetch('/api/ai/costs')
      ]);

      if (modelsRes.ok) {
        const modelsData = await modelsRes.json();
        setModels(modelsData.models);
      }

      if (costsRes.ok) {
        const costsData = await costsRes.json();
        setCostStats(costsData);
      }
    } catch (error) {
      console.error('Error fetching models data:', error);
    }
  };

  const fetchSchedulerConfig = async () => {
    try {
      const response = await fetch('/api/ai/scheduler/status');
      const data = await response.json();

      if (data.config) {
        setSchedulerConfig(data.config);
      }
    } catch (error) {
      console.error('Error fetching scheduler config:', error);
    }
  };

  const updateModelConfig = async () => {
    try {
      const response = await fetch('/api/ai/model-selector/config', {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ config: modelConfig })
      });

      if (response.ok) {
        setModalMessage('AI Model configuration updated successfully!');
        setShowSuccessModal(true);
      } else {
        setModalMessage('Failed to update AI Model configuration');
        setShowErrorModal(true);
      }
    } catch (error) {
      console.error('Error updating model config:', error);
      setModalMessage('Error updating AI Model configuration');
      setShowErrorModal(true);
    }
  };

  const updateSchedulerConfig = async () => {
    try {
      const response = await fetch('/api/ai/scheduler/config', {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ config: schedulerConfig })
      });

      if (response.ok) {
        await fetchSchedulerConfig();
        setModalMessage('AI Scheduler configuration updated successfully!');
        setShowSuccessModal(true);
      } else {
        const error = await response.json();
        setModalMessage(`Failed to update scheduler config: ${error.error}`);
        setShowErrorModal(true);
      }
    } catch (error) {
      console.error('Error updating scheduler config:', error);
      setModalMessage('Error updating scheduler configuration');
      setShowErrorModal(true);
    }
  };

  const resetCosts = async () => {
    if (!confirm('Are you sure you want to reset all cost tracking data?')) {
      return;
    }

    try {
      const response = await fetch('/api/ai/costs/reset', {
        method: 'POST'
      });

      if (response.ok) {
        setCostStats({
          totalCost: 0,
          totalTasks: 0,
          averageCostPerTask: 0,
          modelUsage: {},
          costByComplexity: { simple: 0, medium: 0, complex: 0 }
        });
        setModalMessage('Cost tracking reset successfully!');
        setShowSuccessModal(true);
      } else {
        setModalMessage('Failed to reset cost tracking');
        setShowErrorModal(true);
      }
    } catch (error) {
      console.error('Error resetting costs:', error);
      setModalMessage('Error resetting cost tracking');
      setShowErrorModal(true);
    }
  };

  const getTierColor = (tier) => {
    switch (tier) {
      case 'premium': return '#ef4444';
      case 'balanced': return '#f59e0b';
      case 'efficient': return '#10b981';
      case 'budget': return '#6b7280';
      default: return '#6b7280';
    }
  };



  if (loading) {
    return (
      <div className="settings loading">
        <div className="loading-spinner">
          <SettingsIcon className="animate-spin" size={32} />
          <p>Loading settings...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="settings">
      <div className="settings-header">
        <h1>
          <SettingsIcon size={28} />
          Settings
        </h1>
        <p>Manage AI models, scheduler configuration, and system preferences</p>
      </div>

      {/* Tab Navigation */}
      <div className="settings-tabs">
        <button 
          className={`tab-button ${activeTab === 'ai-models' ? 'active' : ''}`}
          onClick={() => setActiveTab('ai-models')}
        >
          <Brain size={16} />
          AI Models
        </button>
        <button 
          className={`tab-button ${activeTab === 'ai-scheduler' ? 'active' : ''}`}
          onClick={() => setActiveTab('ai-scheduler')}
        >
          <Bot size={16} />
          AI Scheduler
        </button>
      </div>

      <div className="settings-content">
        {activeTab === 'ai-models' && (
          <div className="ai-models-settings">
            {/* Cost Statistics */}
            {costStats && (
              <div className="settings-section">
                <div className="section-header">
                  <h2>
                    <DollarSign size={20} />
                    Cost Statistics
                  </h2>
                  <button className="btn-secondary" onClick={resetCosts}>
                    Reset Tracking
                  </button>
                </div>

                <div className="stats-grid">
                  <div className="stat-card">
                    <div className="stat-icon">
                      <DollarSign size={24} />
                    </div>
                    <div className="stat-content">
                      <h3>${costStats.totalCost?.toFixed(4) || '0.0000'}</h3>
                      <p>Total Cost</p>
                    </div>
                  </div>

                  <div className="stat-card">
                    <div className="stat-icon">
                      <TrendingUp size={24} />
                    </div>
                    <div className="stat-content">
                      <h3>{costStats.totalTasks || 0}</h3>
                      <p>Total Tasks</p>
                    </div>
                  </div>

                  <div className="stat-card">
                    <div className="stat-icon">
                      <Zap size={24} />
                    </div>
                    <div className="stat-content">
                      <h3>${costStats.averageCostPerTask?.toFixed(4) || '0.0000'}</h3>
                      <p>Avg Cost/Task</p>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Model Configuration */}
            <div className="settings-section">
              <div className="section-header">
                <h2>
                  <Brain size={20} />
                  Model Selection Configuration
                </h2>
                <button className="btn-primary" onClick={updateModelConfig}>
                  Update Config
                </button>
              </div>

              <div className="config-grid">
                <div className="config-item">
                  <label>Cost Optimization Strategy</label>
                  <select 
                    value={modelConfig.costOptimization} 
                    onChange={(e) => setModelConfig({...modelConfig, costOptimization: e.target.value})}
                  >
                    <option value="aggressive">Aggressive (Minimize cost)</option>
                    <option value="balanced">Balanced (Cost vs Quality)</option>
                    <option value="quality">Quality (Maximize capability)</option>
                  </select>
                </div>

                <div className="config-item">
                  <label>Max Cost Per Task ($)</label>
                  <input 
                    type="number" 
                    step="0.01" 
                    value={modelConfig.maxCostPerTask}
                    onChange={(e) => setModelConfig({...modelConfig, maxCostPerTask: parseFloat(e.target.value)})}
                  />
                </div>

                <div className="config-item">
                  <label>Quality Threshold (1-10)</label>
                  <input 
                    type="number" 
                    min="1" 
                    max="10" 
                    value={modelConfig.qualityThreshold}
                    onChange={(e) => setModelConfig({...modelConfig, qualityThreshold: parseInt(e.target.value)})}
                  />
                </div>
              </div>
            </div>

            {/* Available Models Summary */}
            <div className="settings-section">
              <div className="section-header">
                <h2>
                  <Brain size={20} />
                  Available Models ({Object.keys(models).length})
                </h2>
              </div>

              <div className="models-summary">
                {Object.entries(models).map(([modelId, model]) => (
                  <div key={modelId} className="model-summary-card">
                    <div className="model-summary-header">
                      <h4>{model.name}</h4>
                      <span
                        className="tier-badge"
                        style={{ backgroundColor: getTierColor(model.tier) }}
                      >
                        {model.tier}
                      </span>
                    </div>
                    <div className="model-summary-details">
                      <span className="model-provider">{model.provider}</span>
                      <span className="model-cost">
                        ${model.costPer1kTokens.input}/${model.costPer1kTokens.output} per 1K tokens
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {activeTab === 'ai-scheduler' && (
          <div className="ai-scheduler-settings">

            {/* Scheduler Configuration */}
            <div className="settings-section">
              <div className="section-header">
                <h2>
                  <SettingsIcon size={20} />
                  Scheduler Configuration
                </h2>
                <button className="btn-primary" onClick={updateSchedulerConfig}>
                  Save Configuration
                </button>
              </div>

              <div className="config-grid">
                <div className="config-item">
                  <label>Tick Rate (ms)</label>
                  <input
                    type="number"
                    value={schedulerConfig.tickRate}
                    onChange={(e) => setSchedulerConfig({...schedulerConfig, tickRate: parseInt(e.target.value)})}
                    min="5000"
                    max="300000"
                  />
                </div>

                <div className="config-item">
                  <label>Max Concurrent Projects</label>
                  <input
                    type="number"
                    value={schedulerConfig.maxConcurrentProjects}
                    onChange={(e) => setSchedulerConfig({...schedulerConfig, maxConcurrentProjects: parseInt(e.target.value)})}
                    min="1"
                    max="10"
                  />
                </div>

                <div className="config-item">
                  <label>Max Stories Per Tick</label>
                  <input
                    type="number"
                    value={schedulerConfig.guardrails.maxStoriesPerTick}
                    onChange={(e) => setSchedulerConfig({
                      ...schedulerConfig,
                      guardrails: {...schedulerConfig.guardrails, maxStoriesPerTick: parseInt(e.target.value)}
                    })}
                    min="1"
                    max="10"
                  />
                </div>

                <div className="config-item">
                  <label>Priority Threshold</label>
                  <select
                    value={schedulerConfig.guardrails.priorityThreshold}
                    onChange={(e) => setSchedulerConfig({
                      ...schedulerConfig,
                      guardrails: {...schedulerConfig.guardrails, priorityThreshold: e.target.value}
                    })}
                  >
                    <option value="low">Low</option>
                    <option value="medium">Medium</option>
                    <option value="high">High</option>
                    <option value="critical">Critical</option>
                  </select>
                </div>
              </div>

              <div className="config-checkboxes">
                <div className="checkbox-item">
                  <label>
                    <input
                      type="checkbox"
                      checked={schedulerConfig.guardrails.requireActiveSprint}
                      onChange={(e) => setSchedulerConfig({
                        ...schedulerConfig,
                        guardrails: {...schedulerConfig.guardrails, requireActiveSprint: e.target.checked}
                      })}
                    />
                    Require Active Sprint
                  </label>
                </div>

                <div className="checkbox-item">
                  <label>
                    <input
                      type="checkbox"
                      checked={schedulerConfig.guardrails.blockOnCriticalIssues}
                      onChange={(e) => setSchedulerConfig({
                        ...schedulerConfig,
                        guardrails: {...schedulerConfig.guardrails, blockOnCriticalIssues: e.target.checked}
                      })}
                    />
                    Block on Critical Issues
                  </label>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Success Modal */}
      <ConfirmationModal
        isOpen={showSuccessModal}
        onClose={() => setShowSuccessModal(false)}
        onConfirm={() => setShowSuccessModal(false)}
        title="Success"
        message={modalMessage}
        confirmText="OK"
        cancelText=""
        type="default"
      />

      {/* Error Modal */}
      <ConfirmationModal
        isOpen={showErrorModal}
        onClose={() => setShowErrorModal(false)}
        onConfirm={() => setShowErrorModal(false)}
        title="Error"
        message={modalMessage}
        confirmText="OK"
        cancelText=""
        type="danger"
      />
    </div>
  );
};

export default Settings;
